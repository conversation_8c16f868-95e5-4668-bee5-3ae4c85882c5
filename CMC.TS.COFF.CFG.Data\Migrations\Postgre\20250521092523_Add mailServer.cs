﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddmailServer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ADDRESS",
                table: "ConfigParameter",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DOMAIN",
                table: "ConfigParameter",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "EMAIL_TEST",
                table: "ConfigParameter",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LOGIN_NAME",
                table: "ConfigParameter",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "NAME",
                table: "ConfigParameter",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PASSWORD",
                table: "ConfigParameter",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PORT",
                table: "ConfigParameter",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "SSL",
                table: "ConfigParameter",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ADDRESS",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "DOMAIN",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "EMAIL_TEST",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "LOGIN_NAME",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "NAME",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "PASSWORD",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "PORT",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "SSL",
                table: "ConfigParameter");
        }
    }
}
