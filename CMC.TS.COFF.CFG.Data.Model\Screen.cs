﻿using CMC.TS.COFF.CFG.Enums;
using CMC.TS.COFF.Helper.Enums;
using CMC.TS.COFF.Helper.Model;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Index(nameof(Code))]
    [Index(nameof(NormalizedCode))]
    [Index(nameof(Name))]
    [Index(nameof(NormalizedName))]
    [Index(nameof(Status))]
    [Index(nameof(ApplicationPageId))]
    [Index(nameof(Type))]
    [Table("Screen")]
    public class Screen : TrackingSystem
    { /// <summary>
      /// Key table
      /// </summary>
        [Key]
        [MaxLength(450)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// Mã giao diện
        /// </summary>
        private string _Code = string.Empty;
        [MaxLength(255)]
        [Required]
        public string Code
        {
            get
            {
                return _Code;
            }
            set
            {
                _Code = value;
                NormalizedCode = value.ToUpperInvariant();
            }
        }
        [MaxLength(255)]
        [Required]
        public string NormalizedCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên giao diện
        /// </summary>
        private string _Name = string.Empty;
        [MaxLength(500)]
        [Required]
        public string Name
        {
            get
            {
                return _Name;
            }
            set
            {
                _Name = value;
                NormalizedName = value.ToUpperInvariant();
            }
        }
        [MaxLength(500)]
        [Required]
        public string NormalizedName { get; set; } = string.Empty;
        /// <summary>
        /// Mô tả
        /// </summary>
        [MaxLength(2000)]
        public string? Description { get; set; }
        /// <summary>
        /// Số thứ tự
        /// </summary>
        public int? SeqNo { get; set; } = 0;
        /// <summary>
        /// Trạng thái: hoạt động/không hoạt động
        /// </summary>
        public ItemStatus Status { get; set; } = ItemStatus.Active;
        /// <summary>
        /// Id page ứng dụng
        /// </summary>
        [MaxLength(450)]
        [ForeignKey("ApplicationPage")]
        public string ApplicationPageId { get; set; } = string.Empty;
        /// <summary>
        /// Danh sách
        /// Thêm mới
        /// Cập nhật
        /// Readonly
        /// Tìm kiếm
        /// Wiki
        /// </summary>
        public ScreenType Type { get; set; }
        public virtual required ApplicationPage ApplicationPage { get; set; }
        public virtual List<FormDefinition>? FormDefinitions { get; set; }
    }
}
