﻿using CMC.TS.COFF.CFG.Biz.Model.EmailConfig;
using CMC.TS.COFF.Helper.Model;
using System.Collections.Generic;

namespace CMC.TS.COFF.CFG.Biz.Model.EmailConfig
{
    public class New : TNewModelBase<string>
    {
        public long EmailConfigCategoryID { get; set; }
        public bool IsValid { get; set; } = true;
        public bool IsDefault { get; set; } = false;
        public bool IsEdited { get; set; } = false;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public List<string> CompanyCodes { get; set; } = new List<string>();
        public List<string> CompanyIds { get; set; } = new List<string>();
        public List<string> FileUploadIds { get; set; } = new List<string>();
    }
}