using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using System;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddEmailConfigCategoryAndCompanyVariable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Không cần xóa bảng EmailConfigCategory vì đã bị xóa

            // 1. Tạo bảng EmailConfigCategory
            migrationBuilder.CreateTable(
                name: "EmailConfigCategory",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    USER_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailConfigCategory", x => x.ID);
                });

            // Không cần xóa bảng CompanyVariable và ConfigParameter vì đã bị xóa

            // 2.1 Tạo bảng ConfigParameter
            migrationBuilder.CreateTable(
                name: "ConfigParameter",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", maxLength: 450, nullable: false, defaultValueSql: "gen_random_uuid()"),
                    RequireDepartmentSelection = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    AutoContractNumberType = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    ContractNumberSuffix = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false, defaultValue: "0"),
                    LockPartnerSignaturePosition = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UseEkyc = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UseContractForm = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UseContractTemplate = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UseContractType = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    RequireRelatedInfoSelection = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UseDepartmentOnContractCreation = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    AllowDepartmentSelection = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    ShowSignersList = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    SelectSignersByCompany = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    FilterSignersByDepartment = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    EmailNotification = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ApiKey = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PreventPartnerDrawSignature = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    PreventPartnerUploadSignature = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UseExpiringSignLink = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    LinkExpirationDays = table.Column<int>(type: "integer", nullable: true),
                    SignatureWidth = table.Column<double>(type: "double precision", nullable: true),
                    SignatureHeight = table.Column<double>(type: "double precision", nullable: true),
                    UseWorkflowByContractType = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    AutoSelectDepartmentByCreator = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    AutoSelectCompanyByCreator = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    ReportUsersByContractType = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false, defaultValue: "0"),
                    UserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConfigParameter", x => x.Id);
                });

            // Không cần xóa các bảng liên quan đến EmailConfig vì đã bị xóa

            // 3. Tạo bảng EmailConfig mới với cấu trúc mới
            migrationBuilder.CreateTable(
                name: "EmailConfig",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_CATEGORY_ID = table.Column<long>(type: "bigint", nullable: false),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IS_DEFAULT = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IS_EDITED = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SUBJECT = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CONTENT = table.Column<string>(type: "text", nullable: false),
                    COMPANY_IDS = table.Column<string>(type: "text", nullable: false, defaultValue: "[]"),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailConfig", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_EMAIL_CONFIG_CATEGORY_EMAIL_CONFIG_CATEGORY_ID",
                        column: x => x.EMAIL_CONFIG_CATEGORY_ID,
                        principalTable: "EmailConfigCategory",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            // 4. Tạo bảng EmailConfigCompany
            migrationBuilder.CreateTable(
                name: "EmailConfigCompany",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    COMPANY_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    COMPANY_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailConfigCompany", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_COMPANY_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            // 5. Tạo bảng NotificationVariable
            migrationBuilder.CreateTable(
                name: "NotificationVariable",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    VARIABLE_KEY = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    VARIABLE_VALUE = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationVariable", x => x.ID);
                    table.ForeignKey(
                        name: "FK_NOTIFICATION_VARIABLE_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            // 6. Tạo bảng EmailConfigAttachment
            migrationBuilder.CreateTable(
                name: "EmailConfigAttachment",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    FILE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FILE_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    BASE64_CONTENT = table.Column<string>(type: "text", nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailConfigAttachment", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_ATTACHMENT_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            // 7. Tạo các chỉ mục
            migrationBuilder.CreateIndex(
                name: "IX_EmailConfig_EMAIL_CONFIG_CATEGORY_ID",
                table: "EmailConfig",
                column: "EMAIL_CONFIG_CATEGORY_ID");

            migrationBuilder.CreateIndex(
                name: "IX_EmailConfigAttachment_EMAIL_CONFIG_ID",
                table: "EmailConfigAttachment",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_EmailConfigCompany_EMAIL_CONFIG_ID",
                table: "EmailConfigCompany",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_EmailConfigCompany_COMPANY_ID",
                table: "EmailConfigCompany",
                column: "COMPANY_ID");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationVariable_EMAIL_CONFIG_ID",
                table: "NotificationVariable",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationVariable_VARIABLE_KEY",
                table: "NotificationVariable",
                column: "VARIABLE_KEY");

            // CompanyVariable đã bị loại bỏ, sử dụng NotificationVariable thay thế
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 1. Xóa bảng EmailConfigAttachment
            migrationBuilder.DropTable(
                name: "EmailConfigAttachment");

            // 2. Xóa bảng EmailConfigCompany
            migrationBuilder.DropTable(
                name: "EmailConfigCompany");

            // 3. Xóa bảng NotificationVariable
            migrationBuilder.DropTable(
                name: "NotificationVariable");

            // 4. Xóa bảng EmailConfig
            migrationBuilder.DropTable(
                name: "EmailConfig");

            // 5. Xóa bảng ConfigParameter
            migrationBuilder.DropTable(
                name: "ConfigParameter");

            // 6. Xóa bảng EmailConfigCategory
            migrationBuilder.DropTable(
                name: "EmailConfigCategory");
        }
    }
}
