﻿using CMC.TS.COFF.CFG.Data.Model;
using CMC.TS.COFF.CFG.Model;
using CMC.TS.COFF.Helper.Data;
using CMC.TS.COFF.Helper.Models;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CMC.TS.COFF.CFG.Data
{
    public class DomainDbContext : DbContext
    {
        protected readonly IConfiguration Configuration;
        protected readonly ILoggerFactory _LoggerFactory;
        protected IHttpContextAccessor HttpContextAccessor { get; }
        protected readonly ITenantService _TenantService;
        public DomainDbContext(IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ILoggerFactory loggerFactory, ITenantService tenantService)
        {
            Configuration = configuration;
            HttpContextAccessor = httpContextAccessor;
            _LoggerFactory = loggerFactory;
            _TenantService = tenantService;
        }
        public DomainDbContext(DbContextOptions<DomainDbContext> dbContextOptions, IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ILoggerFactory loggerFactory, ITenantService tenantService) : base(dbContextOptions)
        {
            Configuration = configuration;
            HttpContextAccessor = httpContextAccessor;
            _LoggerFactory = loggerFactory;
            _TenantService = tenantService;
        }
        #region DbSet
        public DbSet<MenuSet>? MenuSets { get; set; }
        public DbSet<ApplicationPage>? ApplicationPages { get; set; }
        public DbSet<Screen>? Screens { get; set; }
        public DbSet<FormDefinition>? FormDefinitions { get; set; }

        public DbSet<ConfigParameter> ConfigParameters { get; set; }

        public DbSet<EmailConfig> EmailConfigs { get; set; }


        public DbSet<EmailConfigCategory> EmailConfigCategories { get; set; }

        public DbSet<SmsConfig> SmsConfigs { get; set; }



        #endregion
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Data.Model.MenuSet>();
            modelBuilder.Entity<Data.Model.ApplicationPage>();
            modelBuilder.Entity<Data.Model.Screen>();
            modelBuilder.Entity<Data.Model.FormDefinition>();
            modelBuilder.Entity<Data.Model.ConfigParameter>();
            modelBuilder.Entity<Data.Model.EmailConfig>();
            modelBuilder.Entity<Data.Model.EmailConfigCategory>();

            #region Menuset
            modelBuilder.Entity<MenuSet>()
                .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<MenuSet>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<MenuSet>()
                .Property(c => c.Definition).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<List<MenuModel>>(b) ?? new List<MenuModel>());
            #endregion
            #region ApplicationPage
            modelBuilder.Entity<ApplicationPage>()
               .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<ApplicationPage>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            #endregion
            #region Screen
            modelBuilder.Entity<Screen>()
               .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<Screen>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            #endregion
            #region FormDefinition
            modelBuilder.Entity<FormDefinition>()
               .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<FormDefinition>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<FormDefinition>()
                .Property(c => c.InitValue).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<string>(b) ?? "{}");
            modelBuilder.Entity<FormDefinition>()
               .Property(c => c.Definition).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<string>(b) ?? "{}");
            modelBuilder.Entity<FormDefinition>()
               .Property(c => c.OrgCodes).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<string>(b) ?? "{}");
            modelBuilder.Entity<FormDefinition>()
               .Property(c => c.Roles).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<string>(b) ?? "{}");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.Users).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<string>(b) ?? "{}");
            #endregion
            modelBuilder.Entity<ConfigParameter>()
                .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<ConfigParameter>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());

            modelBuilder.Entity<EmailConfig>()
               .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<EmailConfig>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());

            modelBuilder.Entity<EmailConfigCategory>()
              .Property(c => c.CreatedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());
            modelBuilder.Entity<EmailConfigCategory>()
                .Property(c => c.ModifiedBy).HasConversion(b => JsonConvert.SerializeObject(b), b => JsonConvert.DeserializeObject<Person>(b) ?? new Person());

        }


    #region audit user context
    protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options.UseLoggerFactory(_LoggerFactory);
        }
        public override int SaveChanges()
        {
            if (HttpContextAccessor != null && HttpContextAccessor.HttpContext != null
                && HttpContextAccessor.HttpContext.User.Claims.Any())
            {
                this.Audit(HttpContextAccessor.HttpContext.User);
            }
            else
            {
                this.Audit();
            }
            return base.SaveChanges();
        }
        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            if (HttpContextAccessor != null && HttpContextAccessor.HttpContext != null
                && this.HttpContextAccessor.HttpContext.User.Claims.Any())
            {
                this.Audit(HttpContextAccessor.HttpContext.User);
            }
            else
            {
                this.Audit();
            }
            return base.SaveChangesAsync(cancellationToken);
        }
        #endregion
    }
}
