using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace CMC.TS.COFF.CFG.Data.Migrations
{
    public partial class AddSmsNotificationVariable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Tạo bảng SmsNotificationVariable
            migrationBuilder.CreateTable(
                name: "SmsNotificationVariable",
                columns: table => new
                {
                    ID = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SMS_CONFIG_ID = table.Column<long>(nullable: false),
                    VARIABLE_KEY = table.Column<string>(maxLength: 255, nullable: false),
                    VARIABLE_VALUE = table.Column<string>(maxLength: 500, nullable: true),
                    DESCRIPTION = table.Column<string>(maxLength: 500, nullable: true),
                    CREATED_AT = table.Column<DateTime>(nullable: false),
                    UPDATED_AT = table.Column<DateTime>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SmsNotificationVariable", x => x.ID);
                    table.ForeignKey(
                        name: "FK_SmsNotificationVariable_SmsConfig_SMS_CONFIG_ID",
                        column: x => x.SMS_CONFIG_ID,
                        principalTable: "SmsConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            // Tạo index cho SMS_CONFIG_ID
            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationVariable_SMS_CONFIG_ID",
                table: "SmsNotificationVariable",
                column: "SMS_CONFIG_ID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Xóa bảng SmsNotificationVariable
            migrationBuilder.DropTable(
                name: "SmsNotificationVariable");
        }
    }
}
