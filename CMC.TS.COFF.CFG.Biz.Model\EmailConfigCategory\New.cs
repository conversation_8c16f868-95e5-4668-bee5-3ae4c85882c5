using CMC.TS.COFF.CFG.Biz.Model.EmailConfig;
using CMC.TS.COFF.Helper.Model;
using System.Collections.Generic;

namespace CMC.TS.COFF.CFG.Biz.Model.EmailConfigCategory
{
    public class New : TNewModelBase<string>
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool IsValid { get; set; } = true;
        public bool IsDefault { get; set; } = false;
        public string Description { get; set; } = string.Empty;
        public string UserID { get; set; } = string.Empty;
    }
}
