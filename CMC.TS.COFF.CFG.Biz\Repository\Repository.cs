﻿using CMC.TS.COFF.CFG.Biz.Common;
using CMC.TS.COFF.CFG.Enums;


namespace CMC.TS.COFF.CFG.Biz.Common
{
    public class Repository : IRepository
    {
        public IEnumerable<EnumModel> GetValuesEnum(string type, string values)
        {
            Array enumType;
            switch (type)
            {
                case "EmailConfigActionNoti":
                    {
                        enumType = Enum.GetValues(typeof(EmailConfigActionNoti));
                        break;
                    }
                case "EmailConfigTargetType":
                    {
                        enumType = Enum.GetValues(typeof(EmailConfigTargetType));
                        break;
                    }
                case "EmailConfigObjectType":
                    {
                        enumType = Enum.GetValues(typeof(EmailConfigObjectType));
                        break;
                    }
                default:
                    {
                        enumType = Enum.GetValues(typeof(EmailConfigActionType));
                        break;
                    }
            }
            List<int> listValues = null;
            if (!string.IsNullOrWhiteSpace(values))
            {
                listValues = values.Split(new string[] { "," }, StringSplitOptions.None).Select(m => int.Parse(m)).ToList();
            }
            foreach (var item in enumType)
            {
                if (listValues?.Any() == true)
                {
                    if (listValues.Contains((int)item))
                        yield return new EnumModel()
                        {
                            ID = ((int)item),
                            Name = Common.GetEnumDescription((Enum)item)
                        };
                }
                else
                {
                    yield return new EnumModel()
                    {
                        ID = ((int)item),
                        Name = Common.GetEnumDescription((Enum)item)
                    };
                }
            }
        }
    }
}
