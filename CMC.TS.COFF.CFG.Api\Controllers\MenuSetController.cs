﻿using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.Helper.Biz;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MenuSetController : ControllerBase
    {
        #region Ctor
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<MenuSetController> _logger;
        private readonly IConfiguration _configuration;
        public MenuSetController(IRepositoryWrapper repository, ILogger<MenuSetController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }
        #endregion
        #region Biz
        /// <summary>
        /// Tạo bộ menu
        /// Authorized role admin hoặc orgadmin (admin của đơn vị)
        /// </summary>
        /// <param name="model">Model nghiệp cụ Biz.New của MenuSet</param>
        /// <returns>Biz.Model.MenuSet.View nếu thành công</returns>
        [HttpPost]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> Create([FromBody] Biz.Model.MenuSet.New model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.CreateAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy thông tin của MenuSet theo id
        /// </summary>
        /// <param name="id">id MenuSet</param>
        /// <returns>Biz.Model.MenuSet.Edit</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> GetAsync(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.GetAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Update MenuSet
        /// </summary>
        /// <param name="model">Model nghiệp vụ Biz.Edit của MenuSet</param>
        /// <returns>Biz.Model.MenuSet.View nếu thành công</returns>
        [HttpPut]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> UpdateAsync([FromBody] Biz.Model.MenuSet.Edit model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.UpdateAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Xóa bộ menu
        /// </summary>
        /// <param name="id">id của bộ menu</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> DeleteAsync(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.DeleteAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy danh sách bộ menu đang active
        /// </summary>
        /// <param name="filter">Model nghiệp vụ filter MenuSet</param>
        /// <returns>Danh sách phân trang MenuSet</returns>
        [HttpGet("list")]
        public async Task<IActionResult> GetAsync([FromQuery] Biz.Model.MenuSet.Filter filter)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.GetAsync(HttpContext, filter);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy Định nghĩa bộ menu
        /// </summary>
        /// <param name="id">Id của MenuSet</param>
        /// <returns>Danh sách Definition của MenuSet không phân trang</returns>
        [HttpGet("menuset-definition/{id}")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> GetMenuSetDefinitionAsync(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.MenuSetDefinitionAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy thông tin chi tiết của một định nghĩa chức năng trong MenuSet
        /// </summary>
        /// <param name="menuSetId">Id của MenuSet</param>
        /// <param name="itemId">Id của chức năng</param>
        /// <returns>Một MenuModel theo MenuSet và Id chức năng</returns>
        [HttpGet("item-definition/{menuSetId}/{itemId}")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> GetItemDefinitionAsync(string menuSetId, string itemId)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.ItemDefinitionAsync(HttpContext, menuSetId, itemId);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy danh sách chức năng theo MenuSetId và parentId (Nếu có parent id)
        /// </summary>
        /// <param name="menuSetId">Id MenuSet</param>
        /// <param name="parentId">Id của chức năng (Có thể null)</param>
        /// <returns>Danh sách phân trang các chức năng theo</returns>
        [HttpGet("list-definition")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> ListDefinitionAsync([FromQuery] Biz.Model.MenuSet.FilterDefs filter)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.ListDefinitionAsync(HttpContext, filter);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Tạo mới 01 chức năng
        /// </summary>
        /// <param name="model">MenuModel</param>
        /// <param name="menusetId">Id MenuSet</param>
        /// <returns></returns>
        [HttpPost("create-definition")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> CreateDefinition([FromBody] Model.MenuModel model, [FromQuery] string menusetId)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.CreateDefinition(HttpContext, model, menusetId);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Cập nhật 01 chức năng
        /// </summary>
        /// <param name="model">MenuModel</param>
        /// <param name="menusetId">Id của MenuSet</param>
        /// <returns></returns>
        [HttpPut("update-definition")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> UpdateDefinition([FromBody] Model.MenuModel model, [FromQuery] string menusetId)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.UpdateDefinition(HttpContext, model, menusetId);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Xóa 01 item chức năng
        /// </summary>
        /// <param name="model">MenusetId, ParentId, ItemId</param>
        /// <returns></returns>
        [HttpDelete("delete-definition")]
        [Authorize(Roles = "admin,orgadmin")]
        public async Task<IActionResult> DeleteDefinition(Biz.Model.MenuSet.RemoveModel model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.DeleteDefinition(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy menu người dùng theo cấu hình
        /// Ưu tiên người dùng -> Role
        /// OrgCodes chưa implement
        /// </summary>
        /// <returns>Menu người dùng</returns>
        [HttpGet("user-menu")]
        [Authorize]
        public async Task<IActionResult> GetMenu()
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.MenuSet.UserMenu(HttpContext);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
        #endregion
    }
}
