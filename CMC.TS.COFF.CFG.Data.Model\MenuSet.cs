﻿using CMC.TS.COFF.CFG.Model;
using CMC.TS.COFF.Helper.Enums;
using CMC.TS.COFF.Helper.Model;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Table("MenuSet")]
    [Index(nameof(Code))]
    [Index(nameof(Name))]
    [Index(nameof(NormalizedCode))]
    [Index(nameof(NormalizedName))]
    [Index(nameof(Status))]
    [Index(nameof(IsDefault))]
    public class MenuSet : TrackingSystem
    {
        [Key]
        [MaxLength(450)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// Mã bộ menu
        /// </summary>
        private string _Code = string.Empty;
        [MaxLength(255)]
        public string Code
        {
            get
            {
                return _Code;
            }
            set
            {
                _Code = value;
                NormalizedCode = value.ToUpperInvariant();
            }
        }
        [MaxLength(255)]
        public string NormalizedCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên bộ menu
        /// </summary>
        private string _Name = string.Empty;
        [MaxLength(500)]
        public string Name
        {
            get
            {
                return _Name;
            }
            set
            {
                _Name = value;
                NormalizedName = value.ToUpperInvariant();
            }
        }
        [MaxLength(500)]
        public string NormalizedName { get; set; } = string.Empty;
        /// <summary>
        /// Mô tả
        /// </summary>
        [MaxLength(2000)]
        public string? Description { get; set; } = string.Empty;
        /// <summary>
        /// Số thứ tự
        /// </summary>
        public int? SeqNo { get; set; } = 0;
        /// <summary>
        /// Trạng thái
        /// </summary>
        public ItemStatus Status { get; set; } = ItemStatus.Active;
        /// <summary>
        /// Định nghĩa menu
        /// </summary>
        public List<MenuModel> Definition { get; set; } = new List<MenuModel>();
        /// <summary>
        /// Đơn vị
        /// </summary>
        [MaxLength(2000)]
        public string? OrgCodes { get; set; }
        /// <summary>
        /// Vai trò
        /// </summary>
        [MaxLength(2000)]
        public string? Roles { get; set; }
        /// <summary>
        /// Người dùng
        /// </summary>
        [MaxLength(2000)]
        public string? Users { get; set; }
        /// <summary>
        /// Bộ mặc định
        /// </summary>
        public bool IsDefault { get; set; }
    }
}
