﻿using CMC.TS.COFF.CFG.Enums;
using CMC.TS.COFF.Helper.Enums;
using CMC.TS.COFF.Helper.Model;

namespace CMC.TS.COFF.CFG.Biz.Model.Screen
{
    public class Edit : TEditModelBase<string>
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int? SeqNo { get; set; } = 0;
        public ItemStatus Status { get; set; }
        public string ApplicationPageId { get; set; } = Guid.NewGuid().ToString();
        public ScreenType Type { get; set; }
    }
}
