﻿using CMC.TS.COFF.CFG.Enums;
using CMC.TS.COFF.Helper.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Table("SmsConfig")]
    public class SmsConfig : TrackingSystem
    {
        private static readonly char[] _trimChars = new[] { '[', ']', ' ', '"' };
        private static readonly char[] _splitChars = new[] { ',' };

        [Key]
        [Column("ID")]
        public long ID { get; set; }

        [Column("CODE")]
        public String Code { get; set; }


        [Column("NAME")]
        public string? Name { get; set; }


        [Column("COMPANY_IDS")]
        public string CompanyIds { get; set; } = string.Empty;

        [NotMapped]
        public List<string> CompanyIdList
        {
            get
            {
                if (string.IsNullOrEmpty(CompanyIds))
                    return new List<string>();

                try
                {
                  
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(CompanyIds) ?? new List<string>();
                }
                catch
                {
                    var trimmed = CompanyIds.Trim(_trimChars);
                    if (string.IsNullOrEmpty(trimmed))
                        return new List<string>();

                    return trimmed.Split(_splitChars, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim(' ', '"'))
                        .Where(s => !string.IsNullOrEmpty(s))
                        .ToList();
                }
            }
            set => CompanyIds = System.Text.Json.JsonSerializer.Serialize(value ?? new List<string>());
        }

        [Column("CONTENT_VI")]
        [MaxLength]
        public string ContentVi { get; set; } = string.Empty;

        [Column("CONTENT_EN")]
        [MaxLength]
        public string ContentEn { get; set; } = string.Empty;

  
    }
}