﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface IApplicationPage
    {
        Task<IActionResult> CreateAsync(HttpContext httpContext, Biz.Model.ApplicationPage.New model);
        Task<IActionResult> UpdateAsync(HttpContext httpContext, Biz.Model.ApplicationPage.Edit model);
        Task<IActionResult> GetAsync(HttpContext httpContext, string id);
        Task<IActionResult> DeleteAsync(HttpContext context, string id);
        Task<IActionResult> GetAsync(HttpContext context, Biz.Model.ApplicationPage.Filter model);
        Task<IActionResult> GetAsync(HttpContext context);
    }
}
