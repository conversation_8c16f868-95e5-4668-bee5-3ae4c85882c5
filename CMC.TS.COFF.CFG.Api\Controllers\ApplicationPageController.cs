﻿using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.Helper.Biz;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ApplicationPageController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<ApplicationPageController> _logger;
        private readonly IConfiguration _configuration;
        public ApplicationPageController(IRepositoryWrapper repository, ILogger<ApplicationPageController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }

        [Authorize(Roles = "admin")]
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] Biz.Model.ApplicationPage.New model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ApplicationPage.CreateAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [Authorize(Roles = "admin")]
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] Biz.Model.ApplicationPage.Edit model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ApplicationPage.UpdateAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [Authorize(Roles = "admin")]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetAsync(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ApplicationPage.GetAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [Authorize(Roles = "admin")]
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ApplicationPage.DeleteAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [Authorize(Roles = "admin")]
        [HttpGet("list")]
        public async Task<IActionResult> GetAsync([FromQuery] Biz.Model.ApplicationPage.Filter model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ApplicationPage.GetAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [Authorize(Roles = "admin")]
        [HttpGet("short-info")]
        public async Task<IActionResult> GetAsync()
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ApplicationPage.GetAsync(HttpContext);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
    }
}
