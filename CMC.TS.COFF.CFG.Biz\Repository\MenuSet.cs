﻿using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Biz.Model.MenuSet;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.CFG.Data.Model;
using CMC.TS.COFF.CFG.Model;
using CMC.TS.COFF.Helper.Biz;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Extensions;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class MenuSet : RepositoryBase<Data.Model.MenuSet, string>, IMenuSet
    {
        #region Ctor
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;
        public MenuSet(DomainDbContext context, ILogger<RepositoryWrapper> logger, IMapper mapper, IConfiguration configuration, IDistributedCacheService cacheService, ITenantService tenantService) : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
        }
        #endregion

        #region Biz
        public async Task<IActionResult> CreateAsync(HttpContext httpContext, Biz.Model.MenuSet.New model)
        {
            var isExist = _context.MenuSets!.Any(x => x.NormalizedCode.Equals(model.Code.ToUpperInvariant()));
            if (isExist)
            {
                _logger.LogInformation($"MenuSet.CreateAsync exists code: {model.Code}");
                return ResponseMessageWrapper.BuildBadRequest(Message.EXIST_ITEM);
            }
            _logger.LogInformation($"Start create MenuSet with code: {model.Code}");
            var item = _mapper.Map<Biz.Model.MenuSet.New, Data.Model.MenuSet>(model);
            if (item.IsDefault)
            {
                var itemDefault = _context.MenuSets!.FirstOrDefault(x => x.IsDefault);
                if (itemDefault != null)
                {
                    itemDefault.IsDefault = false;
                    _context.MenuSets!.Update(itemDefault);
                }
            }
            await CreateItemAsync(item);
            _logger.LogInformation("MenuSet.CreateAsync succeed");
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.MenuSet, Biz.Model.MenuSet.View>(item));
        }

        public async Task<IActionResult> GetAsync(HttpContext httpContext, string id)
        {
            var item = await _context.MenuSets!.FindAsync(id);
            if (item == null)
            {
                _logger.LogInformation($"MenuSet.GetAsync not found id: {id}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.MenuSet, Model.MenuSet.View>(item));
        }

        public async Task<IActionResult> UpdateAsync(HttpContext httpContext, Biz.Model.MenuSet.Edit model)
        {
            var oldItem = await _context.MenuSets!.FindAsync(model.Id);
            if (oldItem == null)
            {
                _logger.LogInformation($"MenuSet.UpdateAsync not found id: {model.Id}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            if (!model.IsDefault && oldItem.IsDefault)
            {
                _logger.LogInformation($"MenuSet.UpdateAsync must has default: {model.Id}");
                return ResponseMessageWrapper.BuildBadRequest(Constant.Message.MUST_DEFAULT);
            }
            else if (model.IsDefault && !oldItem.IsDefault)
            {
                var itemDefault = _context.MenuSets!.FirstOrDefault(x => x.IsDefault);
                if (itemDefault != null)
                {
                    itemDefault.IsDefault = false;
                    _context.MenuSets!.Update(itemDefault);
                }
            }
            _logger.LogInformation($"MenuSet.UpdateAsync start update: {model.Id}");
            var item = await UpdateItemAsync<Biz.Model.MenuSet.Edit>(model);
            _logger.LogInformation($"MenuSet.UpdateAsync update: {model.Id} succeed");
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.MenuSet, Biz.Model.MenuSet.View>(item));
        }

        public async Task<IActionResult> DeleteAsync(HttpContext httpContext, string id)
        {
            _logger.LogInformation($"MenuSet.DeleteAsync start delete: {id}");
            var menuSet = _context.MenuSets!.FirstOrDefault(x => x.Id.Equals(id));
            if (menuSet == null)
            {
                _logger.LogInformation($"MenuSet.DeleteAsync not found: {id}");
                return ResponseMessageWrapper.BuildNotFound();
            }
            if (menuSet.IsDefault)
            {
                _logger.LogError($"MenuSet.DeleteAsync must have default: {id}");
                return ResponseMessageWrapper.BuildBadRequest(Message.MUST_DEFAULT);
            }
            _context.MenuSets!.Remove(menuSet);
            await _context.SaveChangesAsync();
            _logger.LogInformation($"MenuSet.DeleteAsync delete: {id} succeed");
            return ResponseMessageWrapper.BuildSuccess();
        }

        public async Task<IActionResult> GetAsync(HttpContext httpContext, Biz.Model.MenuSet.Filter filter)
        {
            var items = _context.MenuSets!.AsNoTracking()
                .Where(x => x.Status == Helper.Enums.ItemStatus.Active);
            if (!string.IsNullOrEmpty(filter.NormalizedCode))
            {
                items = items.Where(x => x.NormalizedCode.Contains(filter.NormalizedCode));
            }
            if (!string.IsNullOrEmpty(filter.NormalizedExactlyCode))
            {
                items = items.Where(x => x.NormalizedCode.Equals(filter.NormalizedExactlyCode));
            }
            if (!string.IsNullOrEmpty(filter.NormalizedName))
            {
                items = items.Where(x => x.NormalizedName.Contains(filter.NormalizedName));
            }
            if (filter.IsDefault.HasValue && filter.IsDefault.Value)
            {
                items = items.Where(x => x.IsDefault);
            }
            return ResponseMessageWrapper.BuildSuccess(await items.OrderBy(x => x.SeqNo).GetPagedAsync<Data.Model.MenuSet, Biz.Model.MenuSet.List>(_mapper, filter));
        }

        public async Task<IActionResult> MenuSetDefinitionAsync(HttpContext httpContext, string id)
        {
            var item = await _context.MenuSets!.FindAsync(id);
            if (item == null)
            {
                _logger.LogInformation($"MenuSet.GetAsync not found id: {id}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            if (item.Definition.Count > 0)
                item.Definition = item.Definition.OrderBy(x => x.order).ToList();
            return ResponseMessageWrapper.BuildSuccess(item.Definition);
        }
        public async Task<IActionResult> ItemDefinitionAsync(HttpContext httpContext, string menusetId, string itemId)
        {
            var menuSet = await _context.MenuSets!.FindAsync(menusetId);
            if (menuSet == null)
            {
                _logger.LogInformation($"MenuSet.GetAsync not found id: {menusetId}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            if (menuSet.Definition.Count == 0)
            {
                _logger.LogInformation($"MenuSet.ItemDefinitionAsync definition not define");
                return ResponseMessageWrapper.BuildNotFound(Message.NOT_FOUND);
            }
            var item = FindMenu(menuSet.Definition, itemId);
            if (item == null)
            {
                _logger.LogInformation($"MenuSet.ItemDefinitionAsync not found item: {itemId}");
                return ResponseMessageWrapper.BuildNotFound(Message.NOT_FOUND);
            }
            return ResponseMessageWrapper.BuildSuccess(item);
        }

        public async Task<IActionResult> ListDefinitionAsync(HttpContext httpContext, Biz.Model.MenuSet.FilterDefs filter)
        {
            var menuSet = await _context.MenuSets!.FindAsync(filter.MenuSetId);
            if (menuSet == null)
            {
                _logger.LogInformation($"MenuSet.GetAsync not found id: {filter.MenuSetId}");
                return ResponseMessageWrapper.BuildSuccess((new List<MenuModel>()).GetPaged(filter));
            }
            if (menuSet.Definition.Count == 0)
                return ResponseMessageWrapper.BuildSuccess((new List<MenuModel>()).GetPaged(filter));
            if (string.IsNullOrEmpty(filter.ParentId))
            {
                var items = menuSet.Definition.Where(x => string.IsNullOrEmpty(x.parentkey)).OrderBy(x => x.order).ToList();
                return ResponseMessageWrapper.BuildSuccess(items.GetPaged(filter));
            }
            var item = FindMenu(menuSet.Definition, filter.ParentId);
            if (item == null)
            {
                _logger.LogInformation($"MenuSet.ListDefinitionAsync not found item: {filter.ParentId}");
                return ResponseMessageWrapper.BuildSuccess((new List<MenuModel>()).GetPaged(filter));
            }
            return ResponseMessageWrapper.BuildSuccess((item?.items?.OrderBy(x => x.order).ToList() ?? []).GetPaged(filter));
        }

        public async Task<IActionResult> CreateDefinition(HttpContext httpContext, MenuModel model, string menusetId)
        {
            if (string.IsNullOrEmpty(model.name))
            {
                _logger.LogError("MenuSet.CreateDefinition field name must have value");
                return ResponseMessageWrapper.BuildBadRequest(Constant.Message.MODEL_INVALID);
            }
            var menuSet = await _context.MenuSets!.FindAsync(menusetId);
            if (menuSet == null)
            {
                _logger.LogInformation($"MenuSet.GetAsync not found id: {menusetId}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            var isExist = IsExist(menuSet.Definition, model.name);
            if (isExist)
            {
                _logger.LogError($"MenuSet.CreateDefinition exist name :{model.name}");
                return ResponseMessageWrapper.BuildBadRequest(Message.EXIST_ITEM);
            }
            if (menuSet.Definition.Count == 0 || string.IsNullOrEmpty(model.parentkey))
            {
                _logger.LogDebug($"MenuSet.CreateDefinition add menu model to root");
                model.parentkey = string.Empty;
                menuSet.Definition.Add(model);
            }
            else
            {
                var parentItem = FindMenu(menuSet.Definition, model.parentkey);
                if (parentItem == null)
                {
                    _logger.LogInformation($"MenuSet.CreateDefinition not found parent id: {model.parentkey}");
                    return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
                }
                _logger.LogDebug($"MenuSet.CreateDefinition add menu model to ${model.parentkey}");
                if (parentItem.items == null)
                {
                    parentItem.items = new List<MenuModel>() { model };
                }
                else
                {
                    var currItem = parentItem.items;
                    currItem.Add(model);
                    parentItem.items = currItem;
                }

            }
            _logger.LogInformation($"MenuSet.CreateDefinition start create: {model.name}");
            await UpdateItemAsync(menuSet);
            _logger.LogInformation($"MenuSet.CreateDefinition create: {model.name} succeed");
            return ResponseMessageWrapper.BuildSuccess();
        }

        public async Task<IActionResult> UpdateDefinition(HttpContext httpContext, MenuModel model, string menusetId)
        {
            if (string.IsNullOrEmpty(model.name))
            {
                _logger.LogError("MenuSet.UpdateDefinition field name must have value");
                return ResponseMessageWrapper.BuildBadRequest(Constant.Message.MODEL_INVALID);
            }
            var menuSet = await _context.MenuSets!.FindAsync(menusetId);
            if (menuSet == null)
            {
                _logger.LogInformation($"MenuSet.UpdateDefinition not found id: {menusetId}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            var isExist = IsExist(menuSet.Definition, model.name);
            if (!isExist)
            {
                _logger.LogError($"MenuSet.UpdateDefinition not found name :{model.name}");
                return ResponseMessageWrapper.BuildBadRequest(Message.NOT_FOUND);
            }
            if (menuSet.Definition.Count == 0 || string.IsNullOrEmpty(model.parentkey))
            {
                _logger.LogDebug($"MenuSet.UpdateDefinition add menu model to root");
                var isSucceed = UpdateItem(menuSet.Definition, model);
                if (!isSucceed)
                {
                    _logger.LogError($"MenuSet.UpdateDefinition not found name :{model.name}");
                    return ResponseMessageWrapper.BuildBadRequest(Message.NOT_FOUND);
                }
            }
            else
            {
                var parentItem = FindMenu(menuSet.Definition, model.parentkey);
                if (parentItem == null)
                {
                    _logger.LogInformation($"MenuSet.UpdateDefinition not found parent id: {model.parentkey}");
                    return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
                }
                _logger.LogDebug($"MenuSet.UpdateDefinition add menu model to ${model.parentkey}");
                var isSucceed = UpdateItem(parentItem.items ?? [], model);
                if (!isSucceed)
                {
                    _logger.LogError($"MenuSet.UpdateDefinition not found name :{model.name}");
                    return ResponseMessageWrapper.BuildBadRequest(Message.NOT_FOUND);
                }
            }
            _logger.LogInformation($"MenuSet.UpdateDefinition start update: {model.name}");
            await UpdateItemAsync(menuSet);
            _logger.LogInformation($"MenuSet.UpdateDefinition update: {model.name} succeed");
            return ResponseMessageWrapper.BuildSuccess();
        }

        public async Task<IActionResult> DeleteDefinition(HttpContext httpContext, Biz.Model.MenuSet.RemoveModel model)
        {
            var menuSet = await _context.MenuSets!.FindAsync(model.MenusetId);
            if (menuSet == null)
            {
                _logger.LogInformation($"MenuSet.DeleteDefinition not found id: {model.MenusetId}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            if (menuSet.Definition.Count == 0)
            {
                return ResponseMessageWrapper.BuildBadRequest(Message.NOT_FOUND);
            }
            else
            {
                if (string.IsNullOrEmpty(model.ParentId))
                {
                    menuSet.Definition = menuSet.Definition.Where(x => !x.key.Equals(model.ItemId)).ToList() ?? [];
                }
                else
                {
                    var parentItem = FindMenu(menuSet.Definition, model.ParentId);
                    if (parentItem == null)
                    {
                        _logger.LogInformation($"MenuSet.DeleteDefinition not found parent id: {model.ParentId}");
                        return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
                    }
                    parentItem.items = parentItem.items?.Where(x => !x.key.Equals(model.ItemId)).ToList() ?? [];
                }
            }
            _logger.LogInformation($"MenuSet.DeleteDefinition start delete: {model.ItemId}");
            await UpdateItemAsync(menuSet);
            _logger.LogInformation($"MenuSet.DeleteDefinition {model.ItemId} succeed");
            return ResponseMessageWrapper.BuildSuccess();
        }

        public async Task<IActionResult> UserMenu(HttpContext httpContext)
        {
            var userContext = httpContext.UserContext();
            if (userContext == null)
                return ResponseMessageWrapper.BuildUnauthorized();
            var menuSets = await _context.MenuSets!.Where(x => x.Status == Helper.Enums.ItemStatus.Active).AsNoTracking().ToListAsync();
            if (menuSets == null || menuSets.Count <= 0)
            {
                _logger.LogError($"MenuSet not config");
                return ResponseMessageWrapper.BuildBadRequest(Message.NOT_FOUND);
            }
            var menuSet = menuSets.FirstOrDefault(x => !string.IsNullOrEmpty(x.Users) && x.Users.Contains(userContext.LoginName));
            if (menuSet != null)
            {
                return ResponseMessageWrapper.BuildSuccess(FilterByRole(menuSet.Definition, userContext));
            }
            menuSet = menuSets.FirstOrDefault(x => !string.IsNullOrEmpty(x.Roles) && x.Roles.Contains(userContext.Role));
            if (menuSet != null)
            {
                return ResponseMessageWrapper.BuildSuccess(FilterByRole(menuSet.Definition, userContext));
            }
            menuSet = menuSets.FirstOrDefault(x => x.IsDefault);
            if (menuSet == null)
            {
                _logger.LogError($"MenuSet must have set default");
                return ResponseMessageWrapper.BuildBadRequest(Message.NOT_FOUND);
            }
            return ResponseMessageWrapper.BuildSuccess(FilterByRole(menuSet.Definition, userContext));
        }
        #endregion

        #region Helper
        private bool IsExist(List<MenuModel> defs, string name, string? parentKey = null)
        {
            foreach (var item in defs)
            {
                if (!string.IsNullOrEmpty(item.name) && item.name.Equals(name, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }

                bool isParentMatch = string.IsNullOrEmpty(parentKey) ||
                                     (!string.IsNullOrEmpty(item.parentkey) && item.parentkey.Equals(parentKey));

                if (isParentMatch && item.items?.Count > 0)
                {
                    if (IsExist(item.items, name, item.key))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        private MenuModel? FindMenu(List<MenuModel> defs, string itemId)
        {
            foreach (var item in defs)
            {
                if (item.key.Equals(itemId, StringComparison.OrdinalIgnoreCase))
                {
                    return item;
                }

                if (item.items?.Count > 0)
                {
                    var found = FindMenu(item.items, itemId);
                    if (found != null)
                    {
                        return found;
                    }
                }
            }

            return null;
        }


        private bool UpdateItem(List<MenuModel> defs, MenuModel model)
        {
            var item = defs.FirstOrDefault(x => x.key.Equals(model.key));
            if (item == null) return false;
            item.name = model.name;
            item.label = model.label;
            item.icon = model.icon;
            item.roles = model.roles;
            item.to = model.to;
            item.badge = model.badge;
            item.disabled = model.disabled;
            item.order = model.order;
            item.rolenames = model.rolenames;
            item.users = model.users;
            return true;
        }
        private List<MenuModel> FilterByRole(List<MenuModel> defs, Helper.Models.UserInfo userContext)
        {
            string contextRole = $":#{userContext.Role}:#";
            defs = defs.OrderBy(x => x.order).ToList();
            var currDefs = defs.Where(x => string.IsNullOrEmpty(x.roles) || x.roles.Contains(contextRole));
            if (!currDefs.Any())
                return new List<MenuModel>();
            foreach (var item in currDefs)
            {
                if (item.items?.Count == 0) continue;
                item.items = FilterByRole(item.items ?? [], userContext);
            }
            return currDefs.ToList();
        }
        #endregion

        #region Override
        public override Task<PagedResult<Data.Model.MenuSet>> GetItemsAsync(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public override Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            var items = _context.ApplicationPages!.AsNoTracking().GetPagedAsync<Data.Model.ApplicationPage, TListModel>(_mapper, model);
            return items;
        }
        #endregion
    }
}
