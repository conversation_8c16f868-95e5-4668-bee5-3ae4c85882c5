using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Biz;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class EmailConfigCategory : RepositoryBase<Data.Model.EmailConfigCategory, string>, IEmailConfigCategory
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;

        public EmailConfigCategory(
            DomainDbContext context,
            ILogger<RepositoryWrapper> logger,
            IMapper mapper,
            IConfiguration configuration,
            IDistributedCacheService cacheService,
            ITenantService tenantService) : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
        }

        public async Task<Model.EmailConfigCategory.View> CreateAsync(Biz.Model.EmailConfigCategory.New model)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    // Kiểm tra xem Code đã tồn tại chưa
                    var existingCategory = await _context.EmailConfigCategories
                        .FirstOrDefaultAsync(c => c.Code == model.Code);

                    if (existingCategory != null)
                    {
                        throw new InvalidOperationException($"Category with code '{model.Code}' already exists");
                    }

                    var category = _mapper.Map<Biz.Model.EmailConfigCategory.New, Data.Model.EmailConfigCategory>(model);

                    category.IsDefault = false;
                    _context.EmailConfigCategories.Add(category);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return _mapper.Map<Data.Model.EmailConfigCategory, Model.EmailConfigCategory.View>(category);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError("Create EmailConfigCategory Error: " + ex.ToString());
                    throw;
                }
            }
        }

        public async Task<Model.EmailConfigCategory.View> UpdateAsync(Biz.Model.EmailConfigCategory.Edit model)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    var category = await _context.EmailConfigCategories
                        .FirstOrDefaultAsync(c => c.ID == model.Id);

                    if (category == null)
                        return null;

                    // Update basic properties
                    category.Code = model.Code;
                    category.Name = model.Name;
                    category.IsValid = model.IsValid;
                    category.IsDefault = model.IsDefault;
                    category.Description = model.Description;

                    _context.EmailConfigCategories.Update(category);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync(); // Thêm dòng này để commit transaction

                    return _mapper.Map<Data.Model.EmailConfigCategory, Model.EmailConfigCategory.View>(category);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError("Update EmailConfigCategory Error: " + ex.ToString());
                    throw;
                }
            }
        }

        public async Task<Model.EmailConfigCategory.View> GetAsync(long id)
        {
            try
            {
                var category = await _context.EmailConfigCategories
                    .AsNoTracking() // Tối ưu hiệu suất vì chỉ đọc dữ liệu
                    .FirstOrDefaultAsync(c => c.ID == id);

                if (category == null)
                    return null;

                return _mapper.Map<Data.Model.EmailConfigCategory, Model.EmailConfigCategory.View>(category);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetAsync Error: " + ex.ToString());
                throw;
            }
        }

        public async Task<PagedResult<Model.EmailConfigCategory.View>> GetAsync(int page, int pageSize)
        {
            try
            {
                var query = _context.EmailConfigCategories
                    .AsNoTracking() // Tối ưu hiệu suất vì chỉ đọc dữ liệu
                    .AsQueryable();

                var totalCount = await query.CountAsync();

                // Thực hiện phân trang
                var skip = (page - 1) * pageSize;
                var items = await query
                    .OrderByDescending(c => c.ID)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                var mappedItems = _mapper.Map<List<Data.Model.EmailConfigCategory>, List<Model.EmailConfigCategory.View>>(items);

                return new PagedResult<Model.EmailConfigCategory.View>
                {
                    Page = page,
                    PageSize = pageSize,
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("GetAsync Error: " + ex.ToString());
                return new PagedResult<Model.EmailConfigCategory.View>
                {
                    Page = page,
                    PageSize = pageSize,
                };
            }
        }

        public async Task<List<Model.EmailConfigCategory.ListItem>> GetAllCategoriesOnlyAsync()
        {
            try
            {
                var categories = await _context.EmailConfigCategories
                    .AsNoTracking()
                    .Where(c => c.IsValid)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                return _mapper.Map<List<Data.Model.EmailConfigCategory>, List<Model.EmailConfigCategory.ListItem>>(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetAllCategoriesOnlyAsync Error: " + ex.ToString());
                return new List<Model.EmailConfigCategory.ListItem>();
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    var category = await _context.EmailConfigCategories
                        .Include(c => c.EmailConfigs)
                        .FirstOrDefaultAsync(c => c.ID == id);

                    if (category == null)
                        return false;

                    // Kiểm tra nếu là mẫu mặc định thì không cho xóa
                    if (category.IsDefault)
                    {
                        throw new InvalidOperationException(Message.DELETE_IS_DEFAULT);
                    }

                    // Lấy danh sách ID của các EmailConfig thuộc category này
                    var emailConfigIds = category.EmailConfigs?.Select(e => e.ID).ToList() ?? new List<long>();


                    // Xóa tất cả EmailConfig liên quan
                    if (category.EmailConfigs != null && category.EmailConfigs.Any())
                    {
                        _logger.LogInformation($"Deleting {category.EmailConfigs.Count} email configs related to EmailConfigCategory {id}");
                        _context.EmailConfigs.RemoveRange(category.EmailConfigs);
                        await _context.SaveChangesAsync();
                    }

                    // Remove category
                    _logger.LogInformation($"Deleting EmailConfigCategory {id}");
                    _context.EmailConfigCategories.Remove(category);

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError("Delete EmailConfigCategory Error: " + ex.ToString());
                    throw; // Throw exception để controller có thể bắt và xử lý
                }
            }
        }

        public override Task<PagedResult<Data.Model.EmailConfigCategory>> GetItemsAsync(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public override Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public async Task<IActionResult> ShortInfoAsync()
        {
            var categories = await _context.EmailConfigCategories.Where(x => x.IsValid)
                .Select(x => new Biz.Model.EmailConfigCategory.ShortInfo
                {
                    Code = x.Code,
                    Name = x.Name,
                    Id = x.ID
                }).AsNoTracking().ToListAsync();
            return ResponseMessageWrapper.BuildSuccess(categories);
        }
    }
}
