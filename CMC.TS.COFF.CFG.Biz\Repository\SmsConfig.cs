﻿using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Biz.Model.Organization;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Tenant;
using Ganss.Xss;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;


namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class SmsConfig : RepositoryBase<Data.Model.SmsConfig, string>, ISmsConfig
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;

        public SmsConfig(
            DomainDbContext context,
            ILogger<RepositoryWrapper> logger,
            IMapper mapper,
            IConfiguration configuration,
            IDistributedCacheService cacheService,
            ITenantService tenantService) : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
        }

        public async Task<(Model.SmsConfig.View view, string errorMessage)> CreateAsync(Model.SmsConfig.New model)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    if (!string.IsNullOrEmpty(model.Code))
                    {
                        var existingConfig = await _context.SmsConfigs
                            .FirstOrDefaultAsync(s => s.Code == model.Code);

                        if (existingConfig != null)
                        {
                            _logger.LogWarning($"SmsConfig với Code = {model.Code} đã tồn tại");
                            return (null, $"SmsConfig với Code = {model.Code} đã tồn tại");
                        }
                    }

                    var sanitizer = new HtmlSanitizer();

                    // Tạo mới đối tượng SmsConfig
                    var smsConfig = new Data.Model.SmsConfig
                    {
                        Code = model.Code,
                        Name = model.Name,
                        ContentVi = sanitizer.Sanitize(model.ContentVi),
                        ContentEn = sanitizer.Sanitize(model.ContentEn),
                    };

                    // Thiết lập danh sách công ty
                    if (model.CompanyIds != null && model.CompanyIds.Any())
                    {
                        smsConfig.CompanyIdList = model.CompanyIds;
                    }

                    _context.SmsConfigs.Add(smsConfig);
                    await _context.SaveChangesAsync();

                    

                    // Chuyển đổi sang View model
                    var result = new Model.SmsConfig.View
                    {
                        ID = smsConfig.ID,
                        Code = smsConfig.Code,
                        Name = smsConfig.Name,
                        CompanyIds = smsConfig.CompanyIdList,
                        ContentVi = smsConfig.ContentVi,
                        ContentEn = smsConfig.ContentEn
                    };

                    // Lấy thông tin công ty từ API bên ngoài
                    if (smsConfig.CompanyIdList != null && smsConfig.CompanyIdList.Any())
                    {
                        try
                        {
                            var organizations = new List<OrganizationInfos>();
                            if (organizations != null && organizations.Any())
                            {
                                // Tạo danh sách OrganizationInfo thủ công
                                var companyList = new List<Model.Organization.OrganizationInfos>();
                                foreach (var o in organizations)
                                {
                                    companyList.Add(new Model.Organization.OrganizationInfos
                                    {
                                       
                                      
                                    });
                                }
                                result.Companies = companyList;
                            }
                            else
                            {
                                result.Companies = new List<Model.Organization.OrganizationInfos>();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error getting organizations for SmsConfig {result.ID}");
                            result.Companies = new List<Model.Organization.OrganizationInfos>();
                        }
                    }
                    else
                    {
                        result.Companies = new List<Model.Organization.OrganizationInfos>();
                    }

                    return (result, null);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError("Create SmsConfig Error: " + ex.ToString());
                    return (null, "Lỗi khi tạo SmsConfig: " + ex.Message);
                }
            }
        }

        public async Task<(Model.SmsConfig.View view, string errorMessage)> UpdateAsync(Model.SmsConfig.Edit model)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    var sanitizer = new HtmlSanitizer();

                    // Tìm SmsConfig cần cập nhật
                    _logger.LogInformation($"Đang tìm SmsConfig với ID = {model.ID}");
                    model.ID = long.Parse(model.Id);

                    var smsConfig = await _context.SmsConfigs.FindAsync(model.ID);
                    if (smsConfig == null)
                    {
                        _logger.LogWarning($"Không tìm thấy SmsConfig với ID = {model.ID}");
                        return (null, $"Không tìm thấy SmsConfig với ID = {model.ID}");
                    }
                    _logger.LogInformation($"Đã tìm thấy SmsConfig với ID = {model.ID}");

                    // Kiểm tra xem đã tồn tại SmsConfig khác với Code này chưa
                    if (!string.IsNullOrEmpty(model.Code) && model.Code != smsConfig.Code)
                    {
                        var existingConfig = await _context.SmsConfigs
                            .FirstOrDefaultAsync(s => s.Code == model.Code && s.ID != model.ID);

                        if (existingConfig != null)
                        {
                            _logger.LogWarning($"SmsConfig với Code = {model.Code} đã tồn tại");
                            return (null, $"SmsConfig với Code = {model.Code} đã tồn tại");
                        }
                    }

                    // Cập nhật thông tin
                    smsConfig.Code = model.Code;
                    smsConfig.ContentVi = sanitizer.Sanitize(model.ContentVi);
                    smsConfig.ContentEn = sanitizer.Sanitize(model.ContentEn);
                    smsConfig.Name = model.Name;

                    // Cập nhật danh sách công ty
                    if (model.CompanyIds != null)
                    {
                        smsConfig.CompanyIdList = model.CompanyIds;
                    }

                    _context.SmsConfigs.Update(smsConfig);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    // Chuyển đổi sang View model
                    var result = new Model.SmsConfig.View
                    {
                        ID = smsConfig.ID,
                        Code = smsConfig.Code,
                        Name = smsConfig.Name,
                        CompanyIds = smsConfig.CompanyIdList,
                        ContentVi = smsConfig.ContentVi,
                        ContentEn = smsConfig.ContentEn,
                    };

                    // Lấy thông tin công ty từ API bên ngoài
                    if (smsConfig.CompanyIdList != null && smsConfig.CompanyIdList.Any())
                    {
                        try
                        {
                            var organizations = new List<OrganizationInfos>();
                            if (organizations != null && organizations.Any())
                            {
                                // Tạo danh sách OrganizationInfo thủ công
                                var companyList = new List<Model.Organization.OrganizationInfos>();
                                foreach (var o in organizations)
                                {
                                    companyList.Add(new Model.Organization.OrganizationInfos
                                    {
                                     
                                    });
                                }
                                result.Companies = companyList;
                            }
                            else
                            {
                                result.Companies = new List<Model.Organization.OrganizationInfos>();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error getting organizations for SmsConfig {result.ID}");
                            result.Companies = new List<Model.Organization.OrganizationInfos>();
                        }
                    }
                    else
                    {
                        result.Companies = new List<Model.Organization.OrganizationInfos>();
                    }

                    return (result, null);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError("Update SmsConfig Error: " + ex.ToString());
                    return (null, "Lỗi khi cập nhật SmsConfig: " + ex.Message);
                }
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    var smsConfig = await _context.SmsConfigs.FindAsync(id);
                    if (smsConfig == null)
                    {
                        return false;
                    }
                    _context.SmsConfigs.Remove(smsConfig);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError("Delete SmsConfig Error: " + ex.ToString());
                    return false;
                }
            }
        }

        public async Task<Model.SmsConfig.View> GetAsync(long id)
        {
            try
            {
                var smsConfig = await _context.SmsConfigs
                    .FirstOrDefaultAsync(s => s.ID == id);

                if (smsConfig == null)
                {
                    return null;
                }

                // Chuyển đổi sang View model
                var result = new Model.SmsConfig.View
                {
                    ID = smsConfig.ID,
                    Code = smsConfig.Code,
                    Name = smsConfig.Name,
                    CompanyIds = smsConfig.CompanyIdList,
                    ContentVi = smsConfig.ContentVi,
                    ContentEn = smsConfig.ContentEn,
                };

                // Lấy thông tin công ty từ API bên ngoài
                if (smsConfig.CompanyIdList != null && smsConfig.CompanyIdList.Any())
                {
                    try
                    {
                        var organizations = new List<OrganizationInfos>();
                        if (organizations != null && organizations.Any())
                        {
                            // Tạo danh sách OrganizationInfo thủ công
                            var companyList = new List<Model.Organization.OrganizationInfos>();
                            foreach (var o in organizations)
                            {
                                companyList.Add(new Model.Organization.OrganizationInfos
                                {
                                   
                                });
                            }
                            result.Companies = companyList;
                        }
                        else
                        {
                            result.Companies = new List<Model.Organization.OrganizationInfos>();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error getting organizations for SmsConfig {result.ID}");
                        result.Companies = new List<Model.Organization.OrganizationInfos>();
                    }
                }
                else
                {
                    result.Companies = new List<Model.Organization.OrganizationInfos>();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Get SmsConfig Error: " + ex.ToString());
                return null;
            }
        }

        public async Task<List<Model.SmsConfig.View>> GetAllAsync()
        {
            try
            {
                var smsConfigs = await _context.SmsConfigs
                     .OrderByDescending(s => s.Created)
                    .ToListAsync();

                var result = new List<Model.SmsConfig.View>();

                foreach (var smsConfig in smsConfigs)
                {
                    var viewModel = new Model.SmsConfig.View
                    {
                        ID = smsConfig.ID,
                        Code = smsConfig.Code,
                        Name = smsConfig.Name,
                        CompanyIds = smsConfig.CompanyIdList,
                        ContentVi = smsConfig.ContentVi,
                        ContentEn = smsConfig.ContentEn
                    };

                    // Lấy thông tin công ty từ API bên ngoài
                    if (smsConfig.CompanyIdList != null && smsConfig.CompanyIdList.Any())
                    {
                        try
                        {
                            var organizations = new List<OrganizationInfos>();  
                            if (organizations != null && organizations.Any())
                            {
                                // Tạo danh sách OrganizationInfo thủ công
                                var companyList = new List<Model.Organization.OrganizationInfos>();
                                foreach (var o in organizations)
                                {
                                    companyList.Add(new Model.Organization.OrganizationInfos
                                    {
                                                                  
                                    });
                                }
                                viewModel.Companies = companyList;
                            }
                            else
                            {
                                viewModel.Companies = new List<Model.Organization.OrganizationInfos>();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error getting organizations for SmsConfig {viewModel.ID}");
                            viewModel.Companies = new List<Model.Organization.OrganizationInfos>();
                        }
                    }
                    else
                    {
                        viewModel.Companies = new List<Model.Organization.OrganizationInfos>();
                    }

                    result.Add(viewModel);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Get All SmsConfig Error: " + ex.ToString());
                return new List<Model.SmsConfig.View>();
            }
        }


        public async Task MemoryStreamToEntities(byte[] content)
        {
            var deserialized = ProtoBufHelper.Deserialize<IEnumerable<Model.SmsConfig.View>>(content);

            if (deserialized == null || !deserialized.Any())
                return;

            var entitiesCurrent = await _context.SmsConfigs.ToListAsync();

            // Xử lý dữ liệu thêm mới
            var dataAdd = deserialized.Where(m => !entitiesCurrent.Select(mm => mm.ID).Contains(m.ID));
            foreach (var item in dataAdd)
            {
                var newItem = new Data.Model.SmsConfig
                {
                    Code = item.Code,
                    Name = item.Name,
                    ContentVi = item.ContentVi,
                    ContentEn = item.ContentEn
                };

                if (item.CompanyIds != null && item.CompanyIds.Any())
                {
                    newItem.CompanyIdList = item.CompanyIds;
                }

                _context.SmsConfigs.Add(newItem);
                await _context.SaveChangesAsync();
            }


            var dataUpdate = deserialized.Where(m => entitiesCurrent.Select(mm => mm.ID).Contains(m.ID));
            foreach (var item in dataUpdate)
            {
                var oldItem = entitiesCurrent.FirstOrDefault(e => e.ID == item.ID);
                if (oldItem != null)
                {
                    oldItem.Code = item.Code;
                    oldItem.ContentVi = item.ContentVi;
                    oldItem.ContentEn = item.ContentEn;

                    if (item.CompanyIds != null)
                    {
                        oldItem.CompanyIdList = item.CompanyIds;
                    }

                    _context.SmsConfigs.Update(oldItem);
                    await _context.SaveChangesAsync();

                 
                }
            }

            var dataDelete = entitiesCurrent.Where(m => !deserialized.Select(mm => mm.ID).Contains(m.ID));
            if (dataDelete.Any())
            {
                _context.SmsConfigs.RemoveRange(dataDelete);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<byte[]> EntitiesToMemoryStream()
        {
            try
            {
                var smsConfigs = await _context.SmsConfigs
                    .ToListAsync();

                var result = new List<Model.SmsConfig.View>();

                foreach (var smsConfig in smsConfigs)
                {
                    var viewModel = new Model.SmsConfig.View
                    {
                        ID = smsConfig.ID,
                        Code = smsConfig.Code,
                        Name = smsConfig.Name,
                        CompanyIds = smsConfig.CompanyIdList,
                        ContentVi = smsConfig.ContentVi,
                        ContentEn = smsConfig.ContentEn
                    };

                    // Lấy thông tin công ty từ API bên ngoài
                    if (smsConfig.CompanyIdList != null && smsConfig.CompanyIdList.Any())
                    {
                        try
                        {
                            var organizations = new List<OrganizationInfos>();  
                            if (organizations != null && organizations.Any())
                            {
                                // Tạo danh sách OrganizationInfo thủ công
                                var companyList = new List<Model.Organization.OrganizationInfos>();
                                foreach (var o in organizations)
                                {
                                    companyList.Add(new Model.Organization.OrganizationInfos
                                    {
                                        
                                    });
                                }
                                viewModel.Companies = companyList;
                            }
                            else
                            {
                                viewModel.Companies = new List<Model.Organization.OrganizationInfos>();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error getting organizations for SmsConfig {viewModel.ID}");
                            viewModel.Companies = new List<Model.Organization.OrganizationInfos>();
                        }
                    }
                    else
                    {
                        viewModel.Companies = new List<Model.Organization.OrganizationInfos>();
                    }

                    result.Add(viewModel);
                }

                return ProtoBufHelper.Serialize(result);
            }
            catch (Exception ex)
            {
                _logger.LogError("EntitiesToMemoryStream Error: " + ex.ToString());
                return null;
            }
        }

        public override Task<PagedResult<Data.Model.SmsConfig>> GetItemsAsync(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public override Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            throw new NotImplementedException();
        }


    }
}

