{
    "AllowedHosts": "*",
    "Serilog": {
        "MinimumLevel": {
            "Default": "Debug", // Serilog
            "Override": {
                "Microsoft": "Error", // Dotnet Core
                "System": "Error" // System
            }
        },
        "Using": [
            "Serilog.Sinks.Console",
            "Serilog.Sinks.Async",
            "Serilog.Sinks.File",
            "Serilog.Sinks.Debug"
        ],
        "Enrich": [
            "FromLogContext",
            "WithMachineName",
            "WithThreadId",
            "WithExceptionDetails",
            "WithCorrelationIdHeader"
        ],
        "Properties": {
            "ApplicationName": "coffice.cfg"
        },
        "WriteTo": [
            {
                "Name": "Debug",
                "Args": {
                    "outputTemplate": "[{Timestamp:dd-MM-yyyy HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
                }
            },
            {
                "Name": "Async",
                "Args": {
                    "configure": [
                        {
                            "Name": "Console",
                            "Args": {
                                "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console",
                                "outputTemplate": "[{Timestamp:dd-MM-yyyy HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"
                            }
                        }
                    ]
                }
            }
        ]
    },
    "IsMultipleTenant": false,
    "Tenants": [
        {
            "Id": "1243275B-8736-4C4E-AA3C-F90E0CF2820D",
            "Code": "TenantA",
            "Name": "Tenant A",
            "Secret": ""
        },
        {
            "Id": "05629D9B-CD97-4081-95E0-13A91F93F7F1",
            "Code": "TenantB",
            "Name": "Tenant B",
            "Secret": ""
        }
    ],
    "DatabaseOptions": {
        "Provider": "PostgreSQL", // SQLServer, Oracle, PostgreSQL, MySql, SQLite
        "ConnectionStrings": {
            "SQLServer": "Server=(localdb)\\MSSQLLocalDB;Database=dev_coff_cfg;Trusted_Connection=True;MultipleActiveResultSets=true;",
            "Oracle": "DATA SOURCE=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=PDB)));USER ID=dev_coff_cfg; Password=******",
            "PostgreSQL": "Server=**************;Port=15432;User ID=admin;Password='pz;H8UgB';Database=dev_coff_cfg;Persist Security Info=True;",
            "MySql": "server=**********;database=dev_coff_cfg;User Id=mysql;password=*******$;Persist Security Info=True;",
            "SQLite": "Data Source=Service.db;"
        }
    },
    "SwaggerOptions": {
        "VersionName": "v1",
        "RoutePrefix": ""
    },
    "JwtTokenConfig": {
        "Secret": "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
        "Issuer": "http://cmc.ts.com/c-office",
        "Audience": "http://cmc.ts.com/c-office",
        "AccessTokenExpiration": 120,
        "RefreshTokenExpiration": 120
    },
    "Redis": {
        "Host": "**********:6379",
        "Instance": "COFFICE"
    },
    "RabbitOptions": {
        "Host": "**********:5672",
        "VirtualHost": "/coffice",
        "User": "admin",
        "Password": "*******$"
    },
    "API_GATEWAY": "http://**************:8888/coffice",

    "ExternalApis": {
        "UmsApi": "http://**************:8888"
    }
}