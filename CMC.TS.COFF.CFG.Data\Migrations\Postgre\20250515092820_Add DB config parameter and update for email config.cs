﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBconfigparameterandupdateforemailconfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmailConfigAttachment");

            migrationBuilder.DropTable(
                name: "EmailConfigCompany");

            migrationBuilder.AlterColumn<string>(
                name: "ID",
                table: "ConfigParameter",
                type: "character varying(450)",
                maxLength: 450,
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldMaxLength: 450);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "ID",
                table: "ConfigParameter",
                type: "uuid",
                maxLength: 450,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(450)",
                oldMaxLength: 450);

            migrationBuilder.CreateTable(
                name: "EmailConfigAttachment",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    BASE64_CONTENT = table.Column<string>(type: "text", nullable: false),
                    FILE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FILE_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EMAIL_CONFIG_ATTACHMENT", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_ATTACHMENT_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EmailConfigCompany",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    COMPANY_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    COMPANY_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EMAIL_CONFIG_COMPANY", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_COMPANY_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EMAIL_CONFIG_ATTACHMENT_EMAIL_CONFIG_ID",
                table: "EmailConfigAttachment",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_EMAIL_CONFIG_COMPANY_EMAIL_CONFIG_ID",
                table: "EmailConfigCompany",
                column: "EMAIL_CONFIG_ID");
        }
    }
}
