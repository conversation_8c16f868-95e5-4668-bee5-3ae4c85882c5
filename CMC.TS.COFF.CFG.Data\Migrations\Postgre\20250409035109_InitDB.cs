﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class InitDB : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ApplicationPage",
                columns: table => new
                {
                    ID = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NORMALIZED_CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    NORMALIZED_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SEQ_NO = table.Column<int>(type: "integer", nullable: true),
                    STATUS = table.Column<int>(type: "integer", nullable: false),
                    CREATED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CREATED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    CREATED_BY = table.Column<string>(type: "jsonb", nullable: false),
                    MODIFIED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MODIFIED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    MODIFIED_BY = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_APPLICATION_PAGE", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "MenuSet",
                columns: table => new
                {
                    ID = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NORMALIZED_CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    NORMALIZED_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SEQ_NO = table.Column<int>(type: "integer", nullable: true),
                    STATUS = table.Column<int>(type: "integer", nullable: false),
                    DEFINITION = table.Column<string>(type: "jsonb", nullable: false),
                    ORG_CODES = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    ROLES = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    USERS = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    IS_DEFAULT = table.Column<bool>(type: "boolean", nullable: false),
                    CREATED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CREATED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    CREATED_BY = table.Column<string>(type: "jsonb", nullable: false),
                    MODIFIED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MODIFIED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    MODIFIED_BY = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MENU_SET", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "Screen",
                columns: table => new
                {
                    ID = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NORMALIZED_CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    NORMALIZED_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SEQ_NO = table.Column<int>(type: "integer", nullable: true),
                    STATUS = table.Column<int>(type: "integer", nullable: false),
                    APPLICATION_PAGE_ID = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    TYPE = table.Column<int>(type: "integer", nullable: false),
                    CREATED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CREATED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    CREATED_BY = table.Column<string>(type: "jsonb", nullable: false),
                    MODIFIED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MODIFIED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    MODIFIED_BY = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SCREEN", x => x.ID);
                    table.ForeignKey(
                        name: "FK_SCREEN_APPLICATION_PAGE_APPLICATION_PAGE_ID",
                        column: x => x.APPLICATION_PAGE_ID,
                        principalTable: "ApplicationPage",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FormDefinition",
                columns: table => new
                {
                    ID = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NORMALIZED_CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    NORMALIZED_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SEQ_NO = table.Column<int>(type: "integer", nullable: true),
                    STATUS = table.Column<int>(type: "integer", nullable: false),
                    INIT_VALUE = table.Column<string>(type: "jsonb", nullable: false),
                    DEFINITION = table.Column<string>(type: "jsonb", nullable: false),
                    SCREEN_ID = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    ORG_CODES = table.Column<string>(type: "jsonb", nullable: false),
                    ROLES = table.Column<string>(type: "jsonb", nullable: false),
                    USERS = table.Column<string>(type: "jsonb", nullable: false),
                    IS_DEFAULT = table.Column<bool>(type: "boolean", nullable: false),
                    DEVICE_TYPE = table.Column<int>(type: "integer", nullable: true),
                    FORMULAR = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CREATED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CREATED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    CREATED_BY = table.Column<string>(type: "jsonb", nullable: false),
                    MODIFIED = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MODIFIED_BY_ID = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    MODIFIED_BY = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FORM_DEFINITION", x => x.ID);
                    table.ForeignKey(
                        name: "FK_FORM_DEFINITION_SCREEN_SCREEN_ID",
                        column: x => x.SCREEN_ID,
                        principalTable: "Screen",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_APPLICATION_PAGE_CODE",
                table: "ApplicationPage",
                column: "CODE");

            migrationBuilder.CreateIndex(
                name: "IX_APPLICATION_PAGE_NAME",
                table: "ApplicationPage",
                column: "NAME");

            migrationBuilder.CreateIndex(
                name: "IX_APPLICATION_PAGE_NORMALIZED_CODE",
                table: "ApplicationPage",
                column: "NORMALIZED_CODE");

            migrationBuilder.CreateIndex(
                name: "IX_APPLICATION_PAGE_NORMALIZED_NAME",
                table: "ApplicationPage",
                column: "NORMALIZED_NAME");

            migrationBuilder.CreateIndex(
                name: "IX_APPLICATION_PAGE_STATUS",
                table: "ApplicationPage",
                column: "STATUS");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_CODE",
                table: "FormDefinition",
                column: "CODE");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_IS_DEFAULT",
                table: "FormDefinition",
                column: "IS_DEFAULT");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_NAME",
                table: "FormDefinition",
                column: "NAME");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_NORMALIZED_CODE",
                table: "FormDefinition",
                column: "NORMALIZED_CODE");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_NORMALIZED_NAME",
                table: "FormDefinition",
                column: "NORMALIZED_NAME");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_SCREEN_ID",
                table: "FormDefinition",
                column: "SCREEN_ID");

            migrationBuilder.CreateIndex(
                name: "IX_FORM_DEFINITION_STATUS",
                table: "FormDefinition",
                column: "STATUS");

            migrationBuilder.CreateIndex(
                name: "IX_MENU_SET_CODE",
                table: "MenuSet",
                column: "CODE");

            migrationBuilder.CreateIndex(
                name: "IX_MENU_SET_IS_DEFAULT",
                table: "MenuSet",
                column: "IS_DEFAULT");

            migrationBuilder.CreateIndex(
                name: "IX_MENU_SET_NAME",
                table: "MenuSet",
                column: "NAME");

            migrationBuilder.CreateIndex(
                name: "IX_MENU_SET_NORMALIZED_CODE",
                table: "MenuSet",
                column: "NORMALIZED_CODE");

            migrationBuilder.CreateIndex(
                name: "IX_MENU_SET_NORMALIZED_NAME",
                table: "MenuSet",
                column: "NORMALIZED_NAME");

            migrationBuilder.CreateIndex(
                name: "IX_MENU_SET_STATUS",
                table: "MenuSet",
                column: "STATUS");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_APPLICATION_PAGE_ID",
                table: "Screen",
                column: "APPLICATION_PAGE_ID");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_CODE",
                table: "Screen",
                column: "CODE");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_NAME",
                table: "Screen",
                column: "NAME");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_NORMALIZED_CODE",
                table: "Screen",
                column: "NORMALIZED_CODE");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_NORMALIZED_NAME",
                table: "Screen",
                column: "NORMALIZED_NAME");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_STATUS",
                table: "Screen",
                column: "STATUS");

            migrationBuilder.CreateIndex(
                name: "IX_SCREEN_TYPE",
                table: "Screen",
                column: "TYPE");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FormDefinition");

            migrationBuilder.DropTable(
                name: "MenuSet");

            migrationBuilder.DropTable(
                name: "Screen");

            migrationBuilder.DropTable(
                name: "ApplicationPage");
        }
    }
}
