﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CMC.TS.COFF.CFG.Enums;
using CMC.TS.COFF.Helper.Model;
using Newtonsoft.Json;
using System.Linq;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Table("EmailConfig")]
    public class EmailConfig : TrackingSystem
    {
        [Key]
        public long ID { get; set; }

        [Column("EMAIL_CONFIG_CATEGORY_ID")]
        public long EmailConfigCategoryID { get; set; }


        [Column("IS_VALID")]
        public bool IsValid { get; set; }

        [Column("IS_DEFAULT")]
        public bool IsDefault { get; set; }

        [StringLength(500)]
        [Column("SUBJECT")]
        public string Subject { get; set; } = string.Empty;

        [MaxLength]
        [Column("CONTENT")]
        public string Content { get; set; } = string.Empty;

        [ForeignKey("EmailConfigCategoryID")]
        public virtual EmailConfigCategory EmailConfigCategory { get; set; }

        [Column("COMPANY_IDS")]
        public string? CompanyIds { get; set; }


        [Column("COMPANY_CODES")]
        public string? CompanyCodes { get; set; }

        [Column("FILE_UPLOAD_IDS")]
        public string? FileUploadIds { get; set; }

        // Helper arrays for parsing
        private static readonly char[] _trimChars = { '[', ']', ' ' };
        private static readonly char[] _splitChars = { ',', ';' };

        // NotMapped properties for List<string> conversion
        [NotMapped]
        public List<string> CompanyIdList
        {
            get
            {
                if (string.IsNullOrEmpty(CompanyIds))
                    return new List<string>();

                try
                {
                    // Thử chuyển đổi từ JSON
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(CompanyIds) ?? new List<string>();
                }
                catch
                {
                    // Nếu không phải JSON, thử phân tích chuỗi theo các định dạng khác
                    // Ví dụ: "id1,id2,id3" hoặc "[id1, id2, id3]"
                    var trimmed = CompanyIds.Trim(_trimChars);
                    if (string.IsNullOrEmpty(trimmed))
                        return new List<string>();

                    return trimmed.Split(_splitChars, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim(' ', '"'))
                        .Where(s => !string.IsNullOrEmpty(s))
                        .ToList();
                }
            }
            set => CompanyIds = value == null || !value.Any() ? null : System.Text.Json.JsonSerializer.Serialize(value);
        }

        [NotMapped]
        public List<string> CompanyCodeList
        {
            get
            {
                if (string.IsNullOrEmpty(CompanyCodes))
                    return new List<string>();

                try
                {
                    // Thử chuyển đổi từ JSON
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(CompanyCodes) ?? new List<string>();
                }
                catch
                {
                    // Nếu không phải JSON, thử phân tích chuỗi theo các định dạng khác
                    var trimmed = CompanyCodes.Trim(_trimChars);
                    if (string.IsNullOrEmpty(trimmed))
                        return new List<string>();

                    return trimmed.Split(_splitChars, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim(' ', '"'))
                        .Where(s => !string.IsNullOrEmpty(s))
                        .ToList();
                }
            }
            set => CompanyCodes = value == null || !value.Any() ? null : System.Text.Json.JsonSerializer.Serialize(value);
        }

        [NotMapped]
        public List<string> FileUploadIdList
        {
            get
            {
                if (string.IsNullOrEmpty(FileUploadIds))
                    return new List<string>();

                try
                {
                    // Thử chuyển đổi từ JSON
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(FileUploadIds) ?? new List<string>();
                }
                catch
                {
                    // Nếu không phải JSON, thử phân tích chuỗi theo các định dạng khác
                    var trimmed = FileUploadIds.Trim(_trimChars);
                    if (string.IsNullOrEmpty(trimmed))
                        return new List<string>();

                    return trimmed.Split(_splitChars, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim(' ', '"'))
                        .Where(s => !string.IsNullOrEmpty(s))
                        .ToList();
                }
            }
            set => FileUploadIds = value == null || !value.Any() ? null : System.Text.Json.JsonSerializer.Serialize(value);
        }
    }
}