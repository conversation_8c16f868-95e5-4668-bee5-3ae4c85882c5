﻿using CMC.TS.COFF.Helper.Enums;
using CMC.TS.COFF.Helper.Model;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Index(nameof(Code))]
    [Index(nameof(NormalizedCode))]
    [Index(nameof(Name))]
    [Index(nameof(NormalizedName))]
    [Index(nameof(Status))]
    [Table("ApplicationPage")]
    public class ApplicationPage : TrackingSystem
    {
        /// <summary>
        /// Key table
        /// </summary>
        [Key]
        [MaxLength(450)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// Mã page ứng dụng
        /// </summary>
        private string _Code = string.Empty;
        [Required]
        [MaxLength(255)]
        public string Code
        {
            get
            {
                return _Code;
            }
            set
            {
                _Code = value;
                NormalizedCode = value.ToUpperInvariant();
            }
        }
        [MaxLength(255)]
        public string NormalizedCode { get; set; } = string.Empty;

        /// <summary>
        /// Tên page ứng dụng
        /// </summary>
        private string _Name = string.Empty;
        [Required]
        [MaxLength(500)]
        public string Name
        {
            get
            {
                return _Name;
            }
            set
            {
                _Name = value;
                NormalizedName = value.ToUpperInvariant();
            }
        }
        [MaxLength(500)]
        public string NormalizedName { get; set; } = string.Empty;
        /// <summary>
        /// Mô tả
        /// </summary>
        [MaxLength(2000)]
        public string? Description { get; set; }
        /// <summary>
        /// Số thứ tự
        /// </summary>
        public int? SeqNo { get; set; } = 0;
        /// <summary>
        /// Trạng thái: hoạt động/không hoạt động
        /// </summary>
        public ItemStatus Status { get; set; } = ItemStatus.Active;
        public virtual List<Screen>? Screens { get; set; }
    }
}
