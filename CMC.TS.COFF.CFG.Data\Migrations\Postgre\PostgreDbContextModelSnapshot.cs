﻿// <auto-generated />
using System;
using CMC.TS.COFF.CFG.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    [DbContext(typeof(PostgreDbContext))]
    partial class PostgreDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.14")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.ApplicationPage", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("ID");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("CODE");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("DESCRIPTION");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NAME");

                    b.Property<string>("NormalizedCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("NORMALIZED_CODE");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NORMALIZED_NAME");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("integer")
                        .HasColumnName("SEQ_NO");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("STATUS");

                    b.HasKey("Id")
                        .HasName("PK_APPLICATION_PAGE");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_APPLICATION_PAGE_CODE");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_APPLICATION_PAGE_NAME");

                    b.HasIndex("NormalizedCode")
                        .HasDatabaseName("IX_APPLICATION_PAGE_NORMALIZED_CODE");

                    b.HasIndex("NormalizedName")
                        .HasDatabaseName("IX_APPLICATION_PAGE_NORMALIZED_NAME");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_APPLICATION_PAGE_STATUS");

                    b.ToTable("ApplicationPage");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.ConfigParameter", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("ID");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ADDRESS");

                    b.Property<string>("AllowDepartmentSelection")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("ALLOW_DEPARTMENT_SELECTION");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("API_KEY");

                    b.Property<string>("AutoContractNumberType")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("AUTO_CONTRACT_NUMBER_TYPE");

                    b.Property<string>("AutoSelectCompanyByCreator")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("AUTO_SELECT_COMPANY_BY_CREATOR");

                    b.Property<string>("AutoSelectDepartmentByCreator")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("AUTO_SELECT_DEPARTMENT_BY_CREATOR");

                    b.Property<int?>("ContractExpirationDays")
                        .HasColumnType("integer")
                        .HasColumnName("CONTRACT_EXPIRATION_DAYS");

                    b.Property<string>("ContractNumberSuffix")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("CONTRACT_NUMBER_SUFFIX");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<string>("Domain")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("DOMAIN");

                    b.Property<string>("EmailNotification")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("EMAIL_NOTIFICATION");

                    b.Property<string>("EmailTest")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("EMAIL_TEST");

                    b.Property<string>("FilterSignersByDepartment")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("FILTER_SIGNERS_BY_DEPARTMENT");

                    b.Property<int?>("LinkExpirationDays")
                        .HasColumnType("integer")
                        .HasColumnName("LINK_EXPIRATION_DAYS");

                    b.Property<string>("LockPartnerSignaturePosition")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("LOCK_PARTNER_SIGNATURE_POSITION");

                    b.Property<string>("LoginName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("LOGIN_NAME");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("NAME");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("PASSWORD");

                    b.Property<string>("Port")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("PORT");

                    b.Property<string>("PreventPartnerDrawSignature")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("PREVENT_PARTNER_DRAW_SIGNATURE");

                    b.Property<string>("PreventPartnerUploadSignature")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("PREVENT_PARTNER_UPLOAD_SIGNATURE");

                    b.Property<string>("ReportUsersByContractType")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("REPORT_USERS_BY_CONTRACT_TYPE");

                    b.Property<string>("RequireDepartmentSelection")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("REQUIRE_DEPARTMENT_SELECTION");

                    b.Property<string>("RequireRelatedInfoSelection")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("REQUIRE_RELATED_INFO_SELECTION");

                    b.Property<int?>("SSL")
                        .IsRequired()
                        .HasColumnType("integer")
                        .HasColumnName("SSL");

                    b.Property<string>("SelectSignersByCompany")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("SELECT_SIGNERS_BY_COMPANY");

                    b.Property<string>("ShowSignersList")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("SHOW_SIGNERS_LIST");

                    b.Property<double?>("SignatureHeight")
                        .HasColumnType("double precision")
                        .HasColumnName("SIGNATURE_HEIGHT");

                    b.Property<double?>("SignatureWidth")
                        .HasColumnType("double precision")
                        .HasColumnName("SIGNATURE_WIDTH");

                    b.Property<string>("UseContractExpirationDate")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_CONTRACT_EXPIRATION_DATE");

                    b.Property<string>("UseContractForm")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_CONTRACT_FORM");

                    b.Property<string>("UseContractTemplate")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_CONTRACT_TEMPLATE");

                    b.Property<string>("UseContractType")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_CONTRACT_TYPE");

                    b.Property<string>("UseDepartmentOnContractCreation")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_DEPARTMENT_ON_CONTRACT_CREATION");

                    b.Property<string>("UseEkyc")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_EKYC");

                    b.Property<string>("UseExpiringSignLink")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_EXPIRING_SIGN_LINK");

                    b.Property<string>("UseWorkflowByContractType")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character varying(1)")
                        .HasColumnName("USE_WORKFLOW_BY_CONTRACT_TYPE");

                    b.Property<int>("reminderFrequency")
                        .HasMaxLength(1)
                        .HasColumnType("integer")
                        .HasColumnName("REMINDER_FREQUENCY");

                    b.Property<int>("reminderStartDaysBeforeExpiration")
                        .HasMaxLength(1)
                        .HasColumnType("integer")
                        .HasColumnName("REMINDER_START_DAYS_BEFORE_EXPIRATION");

                    b.HasKey("Id")
                        .HasName("PK_CONFIG_PARAMETER");

                    b.ToTable("ConfigParameter");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.EmailConfig", b =>
                {
                    b.Property<long>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("ID"));

                    b.Property<string>("CompanyCodes")
                        .HasColumnType("text")
                        .HasColumnName("COMPANY_CODES");

                    b.Property<string>("CompanyIds")
                        .HasColumnType("text")
                        .HasColumnName("COMPANY_IDS");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CONTENT");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<long>("EmailConfigCategoryID")
                        .HasColumnType("bigint")
                        .HasColumnName("EMAIL_CONFIG_CATEGORY_ID");

                    b.Property<string>("FileUploadIds")
                        .HasColumnType("text")
                        .HasColumnName("FILE_UPLOAD_IDS");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("IS_DEFAULT");

                    b.Property<bool>("IsValid")
                        .HasColumnType("boolean")
                        .HasColumnName("IS_VALID");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("SUBJECT");

                    b.HasKey("ID")
                        .HasName("PK_EMAIL_CONFIG");

                    b.HasIndex("EmailConfigCategoryID")
                        .HasDatabaseName("IX_EMAIL_CONFIG_EMAIL_CONFIG_CATEGORY_ID");

                    b.ToTable("EmailConfig");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.EmailConfigCategory", b =>
                {
                    b.Property<long>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("ID"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("CODE");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("DESCRIPTION");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("IS_DEFAULT");

                    b.Property<bool>("IsValid")
                        .HasColumnType("boolean")
                        .HasColumnName("IS_VALID");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("NAME");

                    b.HasKey("ID")
                        .HasName("PK_EMAIL_CONFIG_CATEGORY");

                    b.ToTable("EmailConfigCategory");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.FormDefinition", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("ID");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("CODE");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<string>("Definition")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("DEFINITION");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("DESCRIPTION");

                    b.Property<int?>("DeviceType")
                        .HasColumnType("integer")
                        .HasColumnName("DEVICE_TYPE");

                    b.Property<string>("Formular")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("FORMULAR");

                    b.Property<string>("InitValue")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("INIT_VALUE");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("IS_DEFAULT");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NAME");

                    b.Property<string>("NormalizedCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("NORMALIZED_CODE");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NORMALIZED_NAME");

                    b.Property<string>("OrgCodes")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("ORG_CODES");

                    b.Property<string>("Roles")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("ROLES");

                    b.Property<string>("ScreenId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("SCREEN_ID");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("integer")
                        .HasColumnName("SEQ_NO");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("STATUS");

                    b.Property<string>("Users")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("USERS");

                    b.HasKey("Id")
                        .HasName("PK_FORM_DEFINITION");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_FORM_DEFINITION_CODE");

                    b.HasIndex("IsDefault")
                        .HasDatabaseName("IX_FORM_DEFINITION_IS_DEFAULT");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_FORM_DEFINITION_NAME");

                    b.HasIndex("NormalizedCode")
                        .HasDatabaseName("IX_FORM_DEFINITION_NORMALIZED_CODE");

                    b.HasIndex("NormalizedName")
                        .HasDatabaseName("IX_FORM_DEFINITION_NORMALIZED_NAME");

                    b.HasIndex("ScreenId")
                        .HasDatabaseName("IX_FORM_DEFINITION_SCREEN_ID");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_FORM_DEFINITION_STATUS");

                    b.ToTable("FormDefinition");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.MenuSet", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("ID");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("CODE");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<string>("Definition")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("DEFINITION");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("DESCRIPTION");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("IS_DEFAULT");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NAME");

                    b.Property<string>("NormalizedCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("NORMALIZED_CODE");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NORMALIZED_NAME");

                    b.Property<string>("OrgCodes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("ORG_CODES");

                    b.Property<string>("Roles")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("ROLES");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("integer")
                        .HasColumnName("SEQ_NO");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("STATUS");

                    b.Property<string>("Users")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("USERS");

                    b.HasKey("Id")
                        .HasName("PK_MENU_SET");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_MENU_SET_CODE");

                    b.HasIndex("IsDefault")
                        .HasDatabaseName("IX_MENU_SET_IS_DEFAULT");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_MENU_SET_NAME");

                    b.HasIndex("NormalizedCode")
                        .HasDatabaseName("IX_MENU_SET_NORMALIZED_CODE");

                    b.HasIndex("NormalizedName")
                        .HasDatabaseName("IX_MENU_SET_NORMALIZED_NAME");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_MENU_SET_STATUS");

                    b.ToTable("MenuSet");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.Screen", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("ID");

                    b.Property<string>("ApplicationPageId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("APPLICATION_PAGE_ID");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("CODE");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("DESCRIPTION");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NAME");

                    b.Property<string>("NormalizedCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("NORMALIZED_CODE");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("NORMALIZED_NAME");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("integer")
                        .HasColumnName("SEQ_NO");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("STATUS");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("TYPE");

                    b.HasKey("Id")
                        .HasName("PK_SCREEN");

                    b.HasIndex("ApplicationPageId")
                        .HasDatabaseName("IX_SCREEN_APPLICATION_PAGE_ID");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_SCREEN_CODE");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_SCREEN_NAME");

                    b.HasIndex("NormalizedCode")
                        .HasDatabaseName("IX_SCREEN_NORMALIZED_CODE");

                    b.HasIndex("NormalizedName")
                        .HasDatabaseName("IX_SCREEN_NORMALIZED_NAME");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_SCREEN_STATUS");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_SCREEN_TYPE");

                    b.ToTable("Screen");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.SmsConfig", b =>
                {
                    b.Property<long>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("ID"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CODE");

                    b.Property<string>("CompanyCodes")
                        .HasColumnType("text")
                        .HasColumnName("COMPANY_CODES");

                    b.Property<string>("CompanyIds")
                        .HasColumnType("text")
                        .HasColumnName("COMPANY_IDS");

                    b.Property<string>("ContentEn")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CONTENT_EN");

                    b.Property<string>("ContentVi")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CONTENT_VI");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CREATED");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CREATED_BY");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("CREATED_BY_ID");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("MODIFIED");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("MODIFIED_BY");

                    b.Property<string>("ModifiedById")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("MODIFIED_BY_ID");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("NAME");

                    b.HasKey("ID")
                        .HasName("PK_SMS_CONFIG");

                    b.ToTable("SmsConfig");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.EmailConfig", b =>
                {
                    b.HasOne("CMC.TS.COFF.CFG.Data.Model.EmailConfigCategory", "EmailConfigCategory")
                        .WithMany("EmailConfigs")
                        .HasForeignKey("EmailConfigCategoryID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EMAIL_CONFIG_EMAIL_CONFIG_CATEGORY_EMAIL_CONFIG_CATEGORY_ID");

                    b.Navigation("EmailConfigCategory");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.FormDefinition", b =>
                {
                    b.HasOne("CMC.TS.COFF.CFG.Data.Model.Screen", "Screen")
                        .WithMany("FormDefinitions")
                        .HasForeignKey("ScreenId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_FORM_DEFINITION_SCREEN_SCREEN_ID");

                    b.Navigation("Screen");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.Screen", b =>
                {
                    b.HasOne("CMC.TS.COFF.CFG.Data.Model.ApplicationPage", "ApplicationPage")
                        .WithMany("Screens")
                        .HasForeignKey("ApplicationPageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_SCREEN_APPLICATION_PAGE_APPLICATION_PAGE_ID");

                    b.Navigation("ApplicationPage");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.ApplicationPage", b =>
                {
                    b.Navigation("Screens");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.EmailConfigCategory", b =>
                {
                    b.Navigation("EmailConfigs");
                });

            modelBuilder.Entity("CMC.TS.COFF.CFG.Data.Model.Screen", b =>
                {
                    b.Navigation("FormDefinitions");
                });
#pragma warning restore 612, 618
        }
    }
}
