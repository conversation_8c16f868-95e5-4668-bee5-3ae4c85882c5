﻿using CMC.TS.COFF.CFG.Biz.IRepository;

namespace CMC.TS.COFF.CFG.Biz
{
    public interface IRepositoryWrapper
    {
        IRepository.IApplicationPage ApplicationPage { get; }
        IRepository.IScreen Screen { get; }
        IRepository.IFormDefinition FormDefinition { get; }
        IRepository.IMenuSet MenuSet { get; }
        IRepository.IConfigParameter ConfigParameter { get; }
        IRepository.IEmailConfigCategory EmailConfigCategory { get; }
        IRepository.IEmailConfig EmailConfig { get; }
        IRepository.ISmsConfig SmsConfig { get; }
        Common.IRepository CommonRepository { get; }

    }
}