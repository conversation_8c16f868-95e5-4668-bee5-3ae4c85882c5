﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBupdateconfigprameterandsms : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ConfigParameter",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uuid", maxLength: 450, nullable: false),
                    REQUIRE_DEPARTMENT_SELECTION = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    AUTO_CONTRACT_NUMBER_TYPE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    CONTRACT_NUMBER_SUFFIX = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    LOCK_PARTNER_SIGNATURE_POSITION = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USE_EKYC = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USE_CONTRACT_FORM = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USE_CONTRACT_TEMPLATE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USE_CONTRACT_TYPE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    REQUIRE_RELATED_INFO_SELECTION = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USE_DEPARTMENT_ON_CONTRACT_CREATION = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    ALLOW_DEPARTMENT_SELECTION = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    SHOW_SIGNERS_LIST = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    SELECT_SIGNERS_BY_COMPANY = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    FILTER_SIGNERS_BY_DEPARTMENT = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    EMAIL_NOTIFICATION = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    API_KEY = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PREVENT_PARTNER_DRAW_SIGNATURE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    PREVENT_PARTNER_UPLOAD_SIGNATURE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USE_EXPIRING_SIGN_LINK = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    LINK_EXPIRATION_DAYS = table.Column<int>(type: "integer", nullable: true),
                    SIGNATURE_WIDTH = table.Column<double>(type: "double precision", nullable: true),
                    SIGNATURE_HEIGHT = table.Column<double>(type: "double precision", nullable: true),
                    USE_WORKFLOW_BY_CONTRACT_TYPE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    AUTO_SELECT_DEPARTMENT_BY_CREATOR = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    AUTO_SELECT_COMPANY_BY_CREATOR = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    REPORT_USERS_BY_CONTRACT_TYPE = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: false),
                    USER_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CONFIG_PARAMETER", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "EmailConfigCategory",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CODE = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EMAIL_CONFIG_CATEGORY", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "SmsConfig",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WORKFLOW_DEF_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    WORKFLOW_DEF_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ACTION_TYPE = table.Column<int>(type: "integer", nullable: false),
                    OBJECT_TYPE = table.Column<int>(type: "integer", nullable: false),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false),
                    SUBJECT = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CONTENT = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SMS_CONFIG", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "SmsConfigExtend",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WORKFLOW_DEF_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    WORKFLOW_DEF_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ACTION_TYPE = table.Column<long>(type: "bigint", nullable: false),
                    ACTION_TYPE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    OBJECT_TYPE = table.Column<int>(type: "integer", maxLength: 50, nullable: false),
                    OBJECT_TYPE_NAME = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ACTION_NOTI = table.Column<int>(type: "integer", nullable: false),
                    ACTION_NOTI_NAME = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IS_REMIND_WORK = table.Column<bool>(type: "boolean", nullable: false),
                    REMIND_WORK_DAY = table.Column<long>(type: "bigint", nullable: false),
                    CHECK_DUPLICATE_BY_WFS = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SMS_CONFIG_EXTEND", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "EmailConfig",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_CATEGORY_ID = table.Column<long>(type: "bigint", nullable: false),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false),
                    IS_DEFAULT = table.Column<bool>(type: "boolean", nullable: false),
                    IS_EDITED = table.Column<bool>(type: "boolean", nullable: false),
                    SUBJECT = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CONTENT = table.Column<string>(type: "text", nullable: false),
                    COMPANY_IDS = table.Column<string>(type: "text", nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EMAIL_CONFIG", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_EMAIL_CONFIG_CATEGORY_EMAIL_CONFIG_CATEGORY_ID",
                        column: x => x.EMAIL_CONFIG_CATEGORY_ID,
                        principalTable: "EmailConfigCategory",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EmailConfigAttachment",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    FILE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FILE_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    BASE64_CONTENT = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EMAIL_CONFIG_ATTACHMENT", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_ATTACHMENT_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EmailConfigCompany",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    COMPANY_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    COMPANY_NAME = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EMAIL_CONFIG_COMPANY", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EMAIL_CONFIG_COMPANY_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NotificationVariable",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    VARIABLE_KEY = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    VARIABLE_VALUE = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NOTIFICATION_VARIABLE", x => x.ID);
                    table.ForeignKey(
                        name: "FK_NOTIFICATION_VARIABLE_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EMAIL_CONFIG_EMAIL_CONFIG_CATEGORY_ID",
                table: "EmailConfig",
                column: "EMAIL_CONFIG_CATEGORY_ID");

            migrationBuilder.CreateIndex(
                name: "IX_EMAIL_CONFIG_ATTACHMENT_EMAIL_CONFIG_ID",
                table: "EmailConfigAttachment",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_EMAIL_CONFIG_COMPANY_EMAIL_CONFIG_ID",
                table: "EmailConfigCompany",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_NOTIFICATION_VARIABLE_EMAIL_CONFIG_ID",
                table: "NotificationVariable",
                column: "EMAIL_CONFIG_ID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ConfigParameter");

            migrationBuilder.DropTable(
                name: "EmailConfigAttachment");

            migrationBuilder.DropTable(
                name: "EmailConfigCompany");

            migrationBuilder.DropTable(
                name: "NotificationVariable");

            migrationBuilder.DropTable(
                name: "SmsConfig");

            migrationBuilder.DropTable(
                name: "SmsConfigExtend");

            migrationBuilder.DropTable(
                name: "EmailConfig");

            migrationBuilder.DropTable(
                name: "EmailConfigCategory");
        }
    }
}
