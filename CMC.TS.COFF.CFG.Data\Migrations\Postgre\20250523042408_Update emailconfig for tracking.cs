﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class Updateemailconfigfortracking : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CREATED",
                table: "EmailConfigCategory",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY",
                table: "EmailConfigCategory",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY_ID",
                table: "EmailConfigCategory",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "MODIFIED",
                table: "EmailConfigCategory",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY",
                table: "EmailConfigCategory",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY_ID",
                table: "EmailConfigCategory",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CREATED",
                table: "EmailConfig",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY",
                table: "EmailConfig",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY_ID",
                table: "EmailConfig",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "MODIFIED",
                table: "EmailConfig",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY",
                table: "EmailConfig",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY_ID",
                table: "EmailConfig",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CREATED",
                table: "EmailConfigCategory");

            migrationBuilder.DropColumn(
                name: "CREATED_BY",
                table: "EmailConfigCategory");

            migrationBuilder.DropColumn(
                name: "CREATED_BY_ID",
                table: "EmailConfigCategory");

            migrationBuilder.DropColumn(
                name: "MODIFIED",
                table: "EmailConfigCategory");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY",
                table: "EmailConfigCategory");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY_ID",
                table: "EmailConfigCategory");

            migrationBuilder.DropColumn(
                name: "CREATED",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "CREATED_BY",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "CREATED_BY_ID",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "MODIFIED",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY_ID",
                table: "EmailConfig");
        }
    }
}
