using CMC.TS.COFF.Helper.Model;
using System;

namespace CMC.TS.COFF.CFG.Biz.Model.EmailConfigCategory
{
    public class List : TListModelBase<string>
    {
        public long ID { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool IsValid { get; set; } = true;
        public string Description { get; set; } = string.Empty;
        public string UserID { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public int EmailConfigCount { get; set; }
    }
    public class ShortInfo
    {
        public long Id { get; set; }
        public required string Code { get; set; }
        public string? Name { get; set; }
    }
}
