# SmsConfig Company Handling - Giống EmailConfig

## Tóm tắt thay đổi

Đã cập nhật SmsConfig để xử lý CompanyIds và lấy tên Companies giống như EmailConfig, bao gồm:

## 1. Cập nhật Data Model

### SmsConfig.cs
- ✅ Thêm `CompanyCodes` property với column `COMPANY_CODES`
- ✅ Cập nhật `CompanyIds` property để nullable
- ✅ Thêm `CompanyCodeList` property giống EmailConfig
- ✅ Cập nhật helper arrays và JSON parsing logic

<augment_code_snippet path="CMC.TS.COFF.CFG.Data.Model\SmsConfig.cs" mode="EXCERPT">
````csharp
[Column("COMPANY_IDS")]
public string? CompanyIds { get; set; }

[Column("COMPANY_CODES")]
public string? CompanyCodes { get; set; }

// Helper arrays for parsing
private static readonly char[] _trimChars = { '[', ']', ' ' };
private static readonly char[] _splitChars = { ',', ';' };

[NotMapped]
public List<string> CompanyIdList { get; set; }

[NotMapped]
public List<string> CompanyCodeList { get; set; }
````
</augment_code_snippet>

## 2. Database Migration

### Migration File
- ✅ Tạo migration `20250130000000_Add_CompanyCodes_To_SmsConfig.cs`
- ✅ Thêm cột `COMPANY_CODES` vào bảng `SmsConfig`
- ✅ Cập nhật ModelSnapshot

<augment_code_snippet path="CMC.TS.COFF.CFG.Data\Migrations\Postgre\20250130000000_Add_CompanyCodes_To_SmsConfig.cs" mode="EXCERPT">
````csharp
migrationBuilder.AddColumn<string>(
    name: "COMPANY_CODES",
    table: "SmsConfig",
    type: "text",
    nullable: true);
````
</augment_code_snippet>

## 3. Repository Updates

### SmsConfig Repository
- ✅ Thêm `HttpContext` parameter vào tất cả methods cần thiết
- ✅ Lấy thông tin Companies từ API giống EmailConfig
- ✅ Sử dụng `UserInfo` và `ApiClient` với authentication

**Methods đã cập nhật:**
- `CreateAsync(HttpContext httpContext, Model.SmsConfig.New model)`
- `UpdateAsync(HttpContext httpContext, Model.SmsConfig.Edit model)`
- `GetAsync(HttpContext httpContext, long id)`
- `GetAllAsync(HttpContext httpContext)`
- `EntitiesToMemoryStream(HttpContext httpContext)`

<augment_code_snippet path="CMC.TS.COFF.CFG.Biz\Repository\SmsConfig.cs" mode="EXCERPT">
````csharp
// Lấy thông tin công ty từ API giống như EmailConfig
UserInfo currentUser = httpContext.UserContext();
List<OrganizationInfos> organizationInfos = new List<OrganizationInfos>();
ApiClient apiClient = new ApiClient();

var apiResult = await apiClient.GetAsync($"{_configuration["API_GATEWAY"]}/ums/Organization/short-info", user: currentUser);
if (apiResult != null && apiResult.Success)
{
    if (apiResult.Data != null)
    {
        organizationInfos = JsonConvert.DeserializeObject<List<OrganizationInfos>>(apiResult.Data.ToString()) ?? new List<OrganizationInfos>();
    }
}
````
</augment_code_snippet>

## 4. Controller Updates

### SmsConfigController
- ✅ Thêm `HttpContext` parameter vào các method calls
- ✅ Thêm `GetById` endpoint mới
- ✅ Cập nhật tất cả calls đến repository methods

<augment_code_snippet path="CMC.TS.COFF.CFG.Api\Controllers\SmsConfigController.cs" mode="EXCERPT">
````csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(long id)
{
    var item = await _repository.SmsConfig.GetAsync(HttpContext, id);
    if (item == null)
    {
        return ResponseMessageWrapper.BuildNotFound("SMS Config không tồn tại");
    }
    return ResponseMessageWrapper.BuildSuccess(item);
}
````
</augment_code_snippet>

## 5. Business Models

### Đã có sẵn trong các models:
- ✅ `SmsConfig.View` - có `Companies` property
- ✅ `SmsConfig.Edit` - có `Companies` property  
- ✅ `SmsConfig.New` - có `CompanyIds` property

## 6. Mapping Companies

### Logic xử lý giống EmailConfig:
- ✅ Lưu `CompanyIds` dưới dạng JSON array trong database
- ✅ Lấy thông tin Organizations từ UMS API
- ✅ Map CompanyIds với Organizations để lấy tên công ty
- ✅ Trả về `Companies` list trong response

<augment_code_snippet path="CMC.TS.COFF.CFG.Biz\Repository\SmsConfig.cs" mode="EXCERPT">
````csharp
var result = new Model.SmsConfig.View
{
    ID = smsConfig.ID,
    Code = smsConfig.Code,
    Name = smsConfig.Name,
    CompanyIds = smsConfig.CompanyIdList,
    ContentVi = smsConfig.ContentVi,
    ContentEn = smsConfig.ContentEn,
    Companies = organizationInfos?.Where(o => smsConfig.CompanyIdList.Contains(o.Id)).ToList() ?? new List<OrganizationInfos>()
};
````
</augment_code_snippet>

## 7. API Endpoints

### Các endpoints đã cập nhật:
- ✅ `GET /api/SmsConfig` - Lấy tất cả với company names
- ✅ `GET /api/SmsConfig/{id}` - Lấy theo ID với company names  
- ✅ `POST /api/SmsConfig` - Tạo mới với company mapping
- ✅ `PUT /api/SmsConfig` - Cập nhật với company mapping
- ✅ `GET /api/SmsConfig/export` - Export với company names

## 8. Cách chạy migration

```bash
dotnet ef database update --project CMC.TS.COFF.CFG.Data --startup-project CMC.TS.COFF.CFG.Api --context PostgreDbContext
```

## 9. Kiểm tra sau khi migration

```sql
-- Kiểm tra cột mới đã được tạo
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'SmsConfig' 
AND column_name IN ('COMPANY_IDS', 'COMPANY_CODES');
```

## 10. Lưu ý quan trọng

- ✅ **Tương thích ngược**: Các API calls hiện tại vẫn hoạt động
- ✅ **Authentication**: Sử dụng UserContext từ HttpContext để call UMS API
- ✅ **Error handling**: Có try-catch cho API calls
- ✅ **Null safety**: Handle trường hợp API call thất bại
- ✅ **Performance**: Chỉ call API một lần cho mỗi request, không loop

## 11. Test Cases cần kiểm tra

1. **Tạo SmsConfig mới** với CompanyIds
2. **Cập nhật SmsConfig** với CompanyIds khác
3. **Lấy SmsConfig** và verify Companies được populate đúng
4. **Lấy tất cả SmsConfig** và verify performance
5. **Export/Import** SmsConfig với company data
6. **API call thất bại** - verify graceful handling

Tất cả thay đổi đã hoàn thành và SmsConfig giờ xử lý Companies giống hệt EmailConfig!
