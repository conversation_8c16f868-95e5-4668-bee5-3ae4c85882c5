﻿namespace CMC.TS.COFF.CFG.Model
{
    /// <summary>
    /// Model dựng dựa theo MenuModel của primefaces react;
    /// Tham khảo tại https://www.primefaces.org/primereact-v6/#/menumodel
    /// </summary>
    public class MenuModel
    {
        public string key { get; set; } = Guid.NewGuid().ToString();
        public string? parentkey { get; set; }
        public string? name { get; set; }
        public string? order { get; set; }
        public string? label { get; set; }
        public string? icon { get; set; }
        public string? to { get; set; }
        public string? url { get; set; }
        public string? target { get; set; }
        public string? classname { get; set; }
        public string? badge { get; set; }
        public string? badgeclassname { get; set; }
        public string? disabled { get; set; }
        public string? roles { get; set; }
        public string? users { get; set; }
        private List<MenuModel>? _items;
        public List<MenuModel>? items
        {
            get
            {
                return _items?.OrderBy(x => x.order).ToList();
            }
            set
            {
                _items = value;
            }
        }
        public string? rolenames {  get; set; }
        public List<MenuModel>? children { get; set; }

        
    }
}
