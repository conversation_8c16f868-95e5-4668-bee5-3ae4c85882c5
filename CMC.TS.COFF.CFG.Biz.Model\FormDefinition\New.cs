﻿using CMC.TS.COFF.Helper.Enums;

namespace CMC.TS.COFF.CFG.Biz.Model.FormDefinition
{
    public class New
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int? SeqNo { get; set; }
        public ItemStatus Status { get; set; }
        public string InitValue { get; set; } = "{}";
        public string Definition { get; set; } = "{}";
        public string ScreenId { get; set; } = string.Empty;
        public string? OrgCodes { get; set; }
        public string? Roles { get; set; }
        public string? Users { get; set; }
        public bool IsDefault { get; set; }
        public int DeviceType { get; set; }
        public string? Formular { get; set; }
    }
}
