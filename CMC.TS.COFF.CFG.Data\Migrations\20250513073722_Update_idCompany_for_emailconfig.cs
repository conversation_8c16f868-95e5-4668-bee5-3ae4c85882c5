using Microsoft.EntityFrameworkCore.Migrations;

namespace CMC.TS.COFF.CFG.Data.Migrations
{
    public partial class Update_idCompany_for_emailconfig : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Bước 1: Thê<PERSON> cột COMPANY_ID_LIST với giá trị NULL
            migrationBuilder.AddColumn<string[]>(
                name: "COMPANY_ID_LIST",
                table: "EmailConfig",
                nullable: true);

            // Bước 2: Cập nhật giá trị cho cột COMPANY_ID_LIST từ cột COMPANY_IDS
            migrationBuilder.Sql(@"
                UPDATE ""EmailConfig"" 
                SET ""COMPANY_ID_LIST"" = 
                    CASE 
                        WHEN ""COMPANY_IDS"" IS NULL OR ""COMPANY_IDS"" = '' THEN ARRAY[]::text[]
                        ELSE string_to_array(trim(both '[]""' from replace(replace(""COMPANY_IDS"", '""', ''), ',', ',')), ',')
                    END
            ");

            // Bước 3: <PERSON>hay đổi cột COMPANY_ID_LIST thành NOT NULL sau khi đã cập nhật giá trị
            migrationBuilder.AlterColumn<string[]>(
                name: "COMPANY_ID_LIST",
                table: "EmailConfig",
                nullable: false,
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "COMPANY_ID_LIST",
                table: "EmailConfig");
        }
    }
}
