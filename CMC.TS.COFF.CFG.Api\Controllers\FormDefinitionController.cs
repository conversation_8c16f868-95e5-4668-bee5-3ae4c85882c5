﻿using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.Helper.Biz;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FormDefinitionController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<FormDefinitionController> _logger;
        private readonly IConfiguration _configuration;
        public FormDefinitionController(IRepositoryWrapper repository, ILogger<FormDefinitionController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }
        [HttpPost]
        public async Task<IActionResult> Create()
        {
            return ResponseMessageWrapper.BuildSuccess();
        }
    }
}
