using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddUniqueConstraintToEmailConfigCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Xử lý các bản ghi có CODE trùng lặp trước khi thêm unique constraint
            migrationBuilder.Sql(@"
                -- Tạo bảng tạm để lưu các bản ghi có CODE trùng lặp
                CREATE TEMP TABLE duplicate_codes AS
                SELECT ""CODE"", COUNT(*) as count
                FROM ""EmailConfigCategory""
                GROUP BY ""CODE""
                HAVING COUNT(*) > 1;

                -- Cập nhật CODE cho các bản ghi trùng lặp
                UPDATE ""EmailConfigCategory"" e
                SET ""CODE"" = e.""CODE"" || '_' || e.""ID""
                WHERE e.""CODE"" IN (SELECT ""CODE"" FROM duplicate_codes);
            ");

            // Thêm unique constraint cho trường CODE trong bảng EmailConfigCategory
            migrationBuilder.CreateIndex(
                name: "IX_EmailConfigCategory_CODE",
                table: "EmailConfigCategory",
                column: "CODE",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Xóa unique constraint
            migrationBuilder.DropIndex(
                name: "IX_EmailConfigCategory_CODE",
                table: "EmailConfigCategory");
        }
    }
}
