using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using System;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddFileStorage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Xóa bảng FileStorage nếu tồn tại
            migrationBuilder.Sql("DROP TABLE IF EXISTS \"FileStorage\" CASCADE;");

            // Tạo bảng FileStorage
            migrationBuilder.CreateTable(
                name: "FileStorage",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FILE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FILE_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    BASE64_CONTENT = table.Column<string>(type: "text", nullable: false),
                    FILE_SIZE = table.Column<long>(type: "bigint", nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    USER_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    REFERENCE_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    REFERENCE_TYPE = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CONTENT_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileStorage", x => x.ID);
                });

            // Tạo các chỉ mục
            migrationBuilder.CreateIndex(
                name: "IX_FileStorage_FILE_NAME",
                table: "FileStorage",
                column: "FILE_NAME");

            migrationBuilder.CreateIndex(
                name: "IX_FileStorage_REFERENCE_ID",
                table: "FileStorage",
                column: "REFERENCE_ID");

            migrationBuilder.CreateIndex(
                name: "IX_FileStorage_REFERENCE_TYPE",
                table: "FileStorage",
                column: "REFERENCE_TYPE");

            migrationBuilder.CreateIndex(
                name: "IX_FileStorage_USER_ID",
                table: "FileStorage",
                column: "USER_ID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Xóa bảng FileStorage
            migrationBuilder.DropTable(
                name: "FileStorage");
        }
    }
}
