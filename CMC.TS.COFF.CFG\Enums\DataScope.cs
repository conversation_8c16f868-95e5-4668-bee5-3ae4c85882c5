﻿namespace CMC.TS.EDOC.CFG.Enums
{
    /// <summary>
    /// Phạm vi lấy dữ liệu
    /// </summary>
    public enum DataScope
    {
        All = 0, // Lấy toàn bộ
        CurrentOrg = 1, // Trong toàn bộ đơn vị
        CurrentOrgRecursive = 2, // Trong toàn bộ đơn vị và Đơn vị con
        CurrentDepartment = 3, // Trong toàn bộ Bộ phận/Phòng ban
        CurrentDepartmentRecursive = 4, // Trong toàn bộ Bộ phận/Phòng ban trở xuống
        CrossDepartment = 5, // Bộ phận/Phòng ban và ngang cấp
        CrossDepartmentRecursive = 6,// Bộ phận/Phòng ban và ngang cấp và các Bộ phận/Phòng ban con

    }

    //public enum DataScopeV2
    //{
    //    //All = 0, // Lấy toàn bộ
    //    //CurrentOrg = 1, // Trong toàn bộ đơn vị
    //    CurrentOrgRecursive = 2, // Trong toàn bộ đơn vị và Đơn vị con
    //    CurrentDepartment = 3, // Trong toàn bộ Bộ phận/Phòng ban
    //    CurrentDepartmentRecursive = 4, // Trong toàn bộ Bộ phận/Phòng ban trở xuống
    //    CrossDepartment = 5, // Bộ phận/Phòng ban và ngang cấp
    //    CrossDepartmentRecursive = 6,// Bộ phận/Phòng ban và ngang cấp và các Bộ phận/Phòng ban con

    //    All = 0, // Lấy toàn bộ
    //    CurrentOrg = 1, // Trong toàn bộ đơn vị
    //    AAAAAAA
    //    0011000
    //}
}
