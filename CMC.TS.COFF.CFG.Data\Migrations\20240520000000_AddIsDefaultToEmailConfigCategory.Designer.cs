using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace CMC.TS.COFF.CFG.Data.Migrations
{
    [DbContext(typeof(DomainDbContext))]
    [Migration("20240520000000_AddIsDefaultToEmailConfigCategory")]
    partial class AddIsDefaultToEmailConfigCategory
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                .HasAnnotation("ProductVersion", "3.1.32")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            // Các entity khác sẽ được tự động sinh ra khi chạy migration thực tế
            // Đây chỉ là file mẫu
#pragma warning restore 612, 618
        }
    }
}
