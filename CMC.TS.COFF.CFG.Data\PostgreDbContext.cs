﻿using CMC.TS.COFF.CFG.Data.Model;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CMC.TS.COFF.CFG.Data
{
    public class PostgreDbContext : DomainDbContext
    {
        public PostgreDbContext(IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ILoggerFactory loggerFactory, ITenantService tenantService) : base(configuration, httpContextAccessor, loggerFactory, tenantService) { }
        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options.UseNpgsql(_TenantService.ConnectionString).UseUpperSnakeCaseNamingConvention();
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<MenuSet>();
            modelBuilder.Entity<ApplicationPage>();
            modelBuilder.Entity<Screen>();
            modelBuilder.Entity<FormDefinition>();
            modelBuilder.Entity<ConfigParameter>();
            modelBuilder.Entity<EmailConfig>();
            modelBuilder.Entity<EmailConfigCategory>();
            modelBuilder.Entity<SmsConfig>();



            modelBuilder.Entity<MenuSet>()
               .Property(c => c.CreatedBy).HasColumnType("jsonb");
            modelBuilder.Entity<MenuSet>()
              .Property(c => c.ModifiedBy).HasColumnType("jsonb");
            modelBuilder.Entity<MenuSet>()
              .Property(c => c.Definition).HasColumnType("jsonb");

            modelBuilder.Entity<ApplicationPage>()
               .Property(c => c.CreatedBy).HasColumnType("jsonb");
            modelBuilder.Entity<ApplicationPage>()
              .Property(c => c.ModifiedBy).HasColumnType("jsonb");

            modelBuilder.Entity<Screen>()
               .Property(c => c.CreatedBy).HasColumnType("jsonb");
            modelBuilder.Entity<Screen>()
              .Property(c => c.ModifiedBy).HasColumnType("jsonb");

            modelBuilder.Entity<FormDefinition>()
               .Property(c => c.CreatedBy).HasColumnType("jsonb");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.ModifiedBy).HasColumnType("jsonb");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.InitValue).HasColumnType("jsonb");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.Definition).HasColumnType("jsonb");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.OrgCodes).HasColumnType("jsonb");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.Roles).HasColumnType("jsonb");
            modelBuilder.Entity<FormDefinition>()
              .Property(c => c.Users).HasColumnType("jsonb");






        }
    }
}
