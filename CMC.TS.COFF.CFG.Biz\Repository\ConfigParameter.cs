﻿using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Biz.Model.ConfigParameter;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Biz;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class ConfigParameter : RepositoryBase<Data.Model.ConfigParameter, string>, IConfigParameter
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;

        public ConfigParameter(DomainDbContext context, ILogger<RepositoryWrapper> logger, IMapper mapper,
            IConfiguration configuration, IDistributedCacheService cacheService, ITenantService tenantService)
            : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
        }

        public async Task<IActionResult> CreateAsync(HttpContext httpContext, New model)
        {
            _logger.LogDebug("ConfigParameter.CreateAsync start mapper model");
            var item = _mapper.Map<Biz.Model.ConfigParameter.New, Data.Model.ConfigParameter>(model);
            _logger.LogDebug("ConfigParameter.CreateAsync start create ConfigParameter");
            item = await CreateItemAsync(item);
            _logger.LogInformation("ConfigParameter.CreateAsync create succeed");
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.View>(item));
        }

        public async Task<IActionResult> UpdateAsync(HttpContext httpContext, Biz.Model.ConfigParameter.Edit model)
        {
            // En lugar de actualizar, creamos un nuevo registro
            _logger.LogInformation($"ConfigParameter.UpdateAsync creando nuevo registro");

            // Crear un nuevo objeto ConfigParameter
            var newItem = _mapper.Map<Biz.Model.ConfigParameter.Edit, Data.Model.ConfigParameter>(model);

            // Asignar un nuevo ID
            newItem.Id = Guid.NewGuid().ToString();

            // Guardar el nuevo registro
            _context.ConfigParameters.Add(newItem);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"ConfigParameter.UpdateAsync creación exitosa con ID: {newItem.Id}");
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.View>(newItem));
        }

        public async Task<IActionResult> GetLatestAsync(HttpContext httpContext)
        {
            _logger.LogInformation("ConfigParameter.GetLatestAsync: Obteniendo el registro más reciente");

            var latestItem = await _context.ConfigParameters
                .AsNoTracking()
                .OrderByDescending(x => x.Created)
                .FirstOrDefaultAsync();

            if (latestItem == null)
            {
                _logger.LogInformation("ConfigParameter.GetLatestAsync: No se encontraron registros");
                return ResponseMessageWrapper.BuildSuccess(new Biz.Model.ConfigParameter.View());
            }

            _logger.LogInformation($"ConfigParameter.GetLatestAsync: Registro encontrado con ID: {latestItem.Id}");
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.View>(latestItem));
        }


        public async Task<IActionResult> DeleteAsync(HttpContext httpContext, string id)
        {
            _logger.LogInformation($"ConfigParameter.DeleteAsync start delete: {id}");
            var item = await _context.Set<Data.Model.ConfigParameter>().FirstOrDefaultAsync(x => x.Id.Equals(id));
            if (item == null)
            {
                _logger.LogInformation($"ConfigParameter.DeleteAsync not found: {id}");
                return ResponseMessageWrapper.BuildNotFound();
            }
            _context.Set<Data.Model.ConfigParameter>().Remove(item);
            await _context.SaveChangesAsync();
            _logger.LogInformation($"ConfigParameter.DeleteAsync delete: {id} succeed");
            return ResponseMessageWrapper.BuildSuccess();
        }

        public async Task<IActionResult> GetAsync(HttpContext httpContext, string id)
        {
            var item = await _context.Set<Data.Model.ConfigParameter>().FindAsync(id);
            if (item == null)
            {
                _logger.LogInformation($"ConfigParameter.GetAsync not found id: {id}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.View>(item));
        }

        public async Task<IActionResult> GetAsync(HttpContext httpContext, Biz.Model.ConfigParameter.Filter model)
        {
            var items = _context.Set<Data.Model.ConfigParameter>().AsNoTracking();

            if (!string.IsNullOrEmpty(model.RequireDepartmentSelection))
            {
                items = items.Where(x => x.RequireDepartmentSelection.Equals(model.RequireDepartmentSelection));
            }
            if (!string.IsNullOrEmpty(model.UseEkyc))
            {
                items = items.Where(x => x.UseEkyc.Equals(model.UseEkyc));
            }
            if (!string.IsNullOrEmpty(model.UseContractTemplate))
            {
                items = items.Where(x => x.UseContractTemplate.Equals(model.UseContractTemplate));
            }
            if (!string.IsNullOrEmpty(model.UseContractType))
            {
                items = items.Where(x => x.UseContractType.Equals(model.UseContractType));
            }
            if (!string.IsNullOrEmpty(model.EmailNotification))
            {
                items = items.Where(x => x.EmailNotification.Equals(model.EmailNotification));
            }

            return ResponseMessageWrapper.BuildSuccess(await items.OrderBy(x => x.Id).GetPagedAsync<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.List>(_mapper, model));
        }


        public override async Task<PagedResult<Data.Model.ConfigParameter>> GetItemsAsync(PagingRequest model)
        {
            var items = _context.Set<Data.Model.ConfigParameter>().AsNoTracking();
            return await items.OrderBy(x => x.Id).GetPagedAsync<Data.Model.ConfigParameter>(model);
        }

        public override async Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            var items = _context.Set<Data.Model.ConfigParameter>().AsNoTracking();
            return await items.OrderBy(x => x.Id).GetPagedAsync<Data.Model.ConfigParameter, TListModel>(_mapper, model);
        }
    }
}