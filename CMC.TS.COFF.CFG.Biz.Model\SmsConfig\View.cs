﻿using CMC.TS.COFF.CFG.Biz.Model.Organization;
using CMC.TS.COFF.CFG.Enums;
using CMC.TS.COFF.Helper.Model;
using ProtoBuf;
using System;
using System.Collections.Generic;

namespace CMC.TS.COFF.CFG.Biz.Model.SmsConfig
{
    [ProtoContract]
    public class View : TViewModelBase<string>
    {

        [ProtoMember(1)]
        public long ID { get; set; }


        [ProtoMember(2)]
        public string Code { get; set; }

        [ProtoMember(3)]
        public string Name { get; set; }

        [ProtoMember(4)]
        public List<string> CompanyIds { get; set; }

        [ProtoMember(5)]
        public string ContentVi { get; set; }

        [ProtoMember(6)]
        public string ContentEn { get; set; }

        [ProtoMember(7)]
        public List<string> CompanyCodes { get; set; }

        [ProtoMember(8)]
        public List<Model.Organization.OrganizationInfos> Companies { get; set; } = new List<Model.Organization.OrganizationInfos>();
    }
}