﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.PostgreS
{
    /// <inheritdoc />
    public partial class Deletefilestorageandupdateconfigparameter : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FileStorage");

            migrationBuilder.AddColumn<int>(
                name: "REMINDER_FREQUENCY",
                table: "ConfigParameter",
                type: "integer",
                maxLength: 1,
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "REMINDER_START_DAYS_BEFORE_EXPIRATION",
                table: "ConfigParameter",
                type: "integer",
                maxLength: 1,
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "REMINDER_FREQUENCY",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "REMINDER_START_DAYS_BEFORE_EXPIRATION",
                table: "ConfigParameter");

            migrationBuilder.CreateTable(
                name: "FileStorage",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BASE64_CONTENT = table.Column<string>(type: "text", nullable: false),
                    CONTENT_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    FILE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FILE_SIZE = table.Column<long>(type: "bigint", nullable: false),
                    FILE_TYPE = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IS_VALID = table.Column<bool>(type: "boolean", nullable: false),
                    REFERENCE_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    REFERENCE_TYPE = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    USER_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FILE_STORAGE", x => x.ID);
                });
        }
    }
}
