using Microsoft.AspNetCore.Mvc;
using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.Helper.Biz;


namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmailConfigCategoryController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<EmailConfigCategoryController> _logger;
        private readonly IConfiguration _configuration;

        public EmailConfigCategoryController(
            IRepositoryWrapper repository,
            ILogger<EmailConfigCategoryController> logger,
            IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(long id)
        {
            _logger.LogInformation($"Start GetById {id}");
            try
            {
                var item = await _repository.EmailConfigCategory.GetAsync(id);
                if (item == null)
                    return ResponseMessageWrapper.BuildNotFound();
                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Get EmailConfigCategory {id} Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }



        [HttpGet]
        public async Task<IActionResult> GetAllCategoriesOnly()
        {
            _logger.LogInformation($"Start GetAllCategoriesOnly");
            try
            {
                var categories = await _repository.EmailConfigCategory.GetAllCategoriesOnlyAsync();
                return ResponseMessageWrapper.BuildSuccess(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get All Categories Only Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpGet("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            _logger.LogInformation($"Start GetPaged page={page}, pageSize={pageSize}");
            try
            {
                var result = await _repository.EmailConfigCategory.GetAsync(page, pageSize);
                return ResponseMessageWrapper.BuildSuccess(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get Paged EmailConfigCategory Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }



        [HttpPost]
        public async Task<IActionResult> Create([FromBody] Biz.Model.EmailConfigCategory.New model)
        {
            _logger.LogInformation($"Start Create EmailConfigCategory");
            try
            {
                if (model == null)
                    return ResponseMessageWrapper.BuildBadRequest("Model cannot be null");

                var item = await _repository.EmailConfigCategory.CreateAsync(model);
                if (item == null)
                    return ResponseMessageWrapper.BuildErrorResponse(new Exception("Failed to create EmailConfigCategory"));
                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Create EmailConfigCategory Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] Biz.Model.EmailConfigCategory.Edit model)
        {
            _logger.LogInformation($"Start Update EmailConfigCategory");
            try
            {
                if (model == null)
                    return ResponseMessageWrapper.BuildBadRequest("Model cannot be null");

                var item = await _repository.EmailConfigCategory.UpdateAsync(model);
                if (item == null)
                    return ResponseMessageWrapper.BuildErrorResponse(new Exception("Failed to update EmailConfigCategory"));
                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Update EmailConfigCategory Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            _logger.LogInformation($"Start Delete EmailConfigCategory {id}");
            try
            {
                var result = await _repository.EmailConfigCategory.DeleteAsync(id);
                if (!result)
                    return ResponseMessageWrapper.BuildErrorResponse(new Exception("Failed to delete EmailConfigCategory"));
                return ResponseMessageWrapper.BuildSuccess();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Delete EmailConfigCategory {id} Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpGet("short-info")]
        public async Task<IActionResult> ShortInfoAsync()
        {
            _logger.LogInformation($"Start ShortInfoAsyn");
            try
            {
               return await _repository.EmailConfigCategory.ShortInfoAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"ShortInfoAsync errors: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
    }
}
