﻿using AutoMapper;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CMC.TS.COFF.CFG.Biz
{
    public class RepositoryWrapper : IRepositoryWrapper
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public RepositoryWrapper(DomainDbContext context, ILogger<RepositoryWrapper> logger, IConfiguration configuration,
            IDistributedCacheService cacheService, ITenantService tenantService, 
            IHttpContextAccessor httpContextAccessor)
        {
            _context = context;
            _logger = logger;
            _mapper = Mapper();
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
            _httpContextAccessor = httpContextAccessor;
        }

        public static IMapper Mapper()
        {
            MapperConfiguration mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new MappingProfile());
            });
            return mappingConfig.CreateMapper();
        }

        private IRepository.IApplicationPage? _ApplicationPage = null;
        public IRepository.IApplicationPage ApplicationPage => _ApplicationPage ??= new Repository.ApplicationPage(_context, _logger, _mapper, _configuration, _cacheService, _tenantService);

        private IRepository.IScreen? _Screen = null;
        public IRepository.IScreen Screen => _Screen ??= new Repository.Screen(_context, _logger, _mapper, _configuration, _cacheService, _tenantService);

        private IRepository.IFormDefinition? _FormDefinition = null;
        public IRepository.IFormDefinition FormDefinition => _FormDefinition ??= new Repository.FormDefinition(_context, _logger, _mapper, _configuration, _cacheService, _tenantService);

        private IRepository.IMenuSet? _MenuSet = null;
        public IRepository.IMenuSet MenuSet => _MenuSet ??= new Repository.MenuSet(_context, _logger, _mapper, _configuration, _cacheService, _tenantService);

        private IRepository.IConfigParameter? _ConfigParameter = null;
        public IRepository.IConfigParameter ConfigParameter => _ConfigParameter ??= new Repository.ConfigParameter(_context, _logger, _mapper, _configuration, _cacheService, _tenantService) as IRepository.IConfigParameter;

        private IRepository.IEmailConfigCategory? _EmailConfigCategory = null;
        public IRepository.IEmailConfigCategory EmailConfigCategory => _EmailConfigCategory ??= new Repository.EmailConfigCategory(_context, _logger, _mapper, _configuration, _cacheService, _tenantService);

        private IRepository.IEmailConfig? _EmailConfig = null;
        public IRepository.IEmailConfig EmailConfig => _EmailConfig ??= new Repository.EmailConfig(_context, _logger, _mapper, _configuration, _cacheService, _tenantService,  _httpContextAccessor);


        private IRepository.ISmsConfig? _SmsConfig = null;
        public IRepository.ISmsConfig SmsConfig => _SmsConfig ??= new Repository.SmsConfig(_context, _logger, _mapper, _configuration, _cacheService, _tenantService);


        private Common.IRepository _commonRepository;
        public Common.IRepository CommonRepository => _commonRepository ?? (_commonRepository = new Common.Repository());
    }
}