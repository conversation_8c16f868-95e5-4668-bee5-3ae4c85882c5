﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBconfigparameterupdate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CREATED",
                table: "ConfigParameter",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY",
                table: "ConfigParameter",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY_ID",
                table: "ConfigParameter",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "MODIFIED",
                table: "ConfigParameter",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY",
                table: "ConfigParameter",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY_ID",
                table: "ConfigParameter",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CREATED",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "CREATED_BY",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "CREATED_BY_ID",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "MODIFIED",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY",
                table: "ConfigParameter");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY_ID",
                table: "ConfigParameter");
        }
    }
}
