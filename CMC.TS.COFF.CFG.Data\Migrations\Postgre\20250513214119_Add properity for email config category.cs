﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class Addproperityforemailconfigcategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "COMPANY_ID_LIST",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "FILE_UPLOAD_ID_LIST",
                table: "EmailConfig");

            migrationBuilder.AddColumn<bool>(
                name: "IS_DEFAULT",
                table: "EmailConfigCategory",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IS_DEFAULT",
                table: "EmailConfigCategory");

            migrationBuilder.AddColumn<List<string>>(
                name: "COMPANY_ID_LIST",
                table: "EmailConfig",
                type: "text[]",
                nullable: false);

            migrationBuilder.AddColumn<List<long>>(
                name: "FILE_UPLOAD_ID_LIST",
                table: "EmailConfig",
                type: "bigint[]",
                nullable: false);
        }
    }
}
