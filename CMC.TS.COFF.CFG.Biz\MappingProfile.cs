﻿using AutoMapper;
using CMC.TS.COFF.CFG.Model;

namespace CMC.TS.COFF.CFG.Biz
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<Data.Model.MenuSet, Biz.Model.MenuSet.New>().ReverseMap();
            CreateMap<Data.Model.MenuSet, Biz.Model.MenuSet.Edit>().ReverseMap();
            CreateMap<Data.Model.MenuSet, Biz.Model.MenuSet.List>().ReverseMap();
            CreateMap<Data.Model.MenuSet, Biz.Model.MenuSet.View>().ReverseMap();

            CreateMap<Data.Model.ApplicationPage, Biz.Model.ApplicationPage.New>().ReverseMap();
            CreateMap<Data.Model.ApplicationPage, Biz.Model.ApplicationPage.Edit>().ReverseMap();
            CreateMap<Data.Model.ApplicationPage, Biz.Model.ApplicationPage.View>().ReverseMap();
            CreateMap<Data.Model.ApplicationPage, Biz.Model.ApplicationPage.List>().ReverseMap();

            CreateMap<Data.Model.Screen, Biz.Model.Screen.New>().ReverseMap();
            CreateMap<Data.Model.Screen, Biz.Model.Screen.Edit>().ReverseMap();
            CreateMap<Data.Model.Screen, Biz.Model.Screen.View>().ReverseMap();
            CreateMap<Data.Model.Screen, Biz.Model.Screen.List>().ReverseMap();

            CreateMap<Data.Model.FormDefinition, Biz.Model.FormDefinition.New>().ReverseMap();
            CreateMap<Data.Model.FormDefinition, Biz.Model.FormDefinition.Edit>().ReverseMap();
            CreateMap<Data.Model.FormDefinition, Biz.Model.FormDefinition.View>().ReverseMap();
            CreateMap<Data.Model.FormDefinition, Biz.Model.FormDefinition.List>().ReverseMap();

            CreateMap<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.New>().ReverseMap();
            CreateMap<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.Edit>().ReverseMap();
            CreateMap<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.View>().ReverseMap();
            CreateMap<Data.Model.ConfigParameter, Biz.Model.ConfigParameter.List>().ReverseMap();



            // EmailConfigCategory mappings
            CreateMap<Data.Model.EmailConfigCategory, Biz.Model.EmailConfigCategory.New>().ReverseMap();
            CreateMap<Data.Model.EmailConfigCategory, Biz.Model.EmailConfigCategory.Edit>().ReverseMap();
            CreateMap<Data.Model.EmailConfigCategory, Biz.Model.EmailConfigCategory.View>().ReverseMap();
            CreateMap<Data.Model.EmailConfigCategory, Biz.Model.EmailConfigCategory.List>()
                .ForMember(dest => dest.EmailConfigCount, opt => opt.MapFrom(src => src.EmailConfigs.Count))
                .ReverseMap();
            CreateMap<Data.Model.EmailConfigCategory, Biz.Model.EmailConfigCategory.ListItem>();
            CreateMap<Data.Model.EmailConfigCategory, Biz.Model.EmailConfigCategory.ShortInfo>();

            // EmailConfig mappings
            CreateMap<Data.Model.EmailConfig, Biz.Model.EmailConfig.New>()
                .ForMember(dest => dest.CompanyIds, opt => opt.MapFrom(src => src.CompanyIdList))
                .ForMember(dest => dest.CompanyCodes, opt => opt.MapFrom(src => src.CompanyCodeList))
                .ForMember(dest => dest.FileUploadIds, opt => opt.MapFrom(src => src.FileUploadIdList));

            CreateMap<Biz.Model.EmailConfig.New, Data.Model.EmailConfig>()
                .ForMember(dest => dest.CompanyIdList, opt => opt.MapFrom(src => src.CompanyIds))
                .ForMember(dest => dest.CompanyCodeList, opt => opt.MapFrom(src => src.CompanyCodes))
                .ForMember(dest => dest.FileUploadIdList, opt => opt.MapFrom(src => src.FileUploadIds));

            CreateMap<Data.Model.EmailConfig, Biz.Model.EmailConfig.Edit>()
                .ForMember(dest => dest.CompanyIds, opt => opt.MapFrom(src => src.CompanyIdList))
                .ForMember(dest => dest.CompanyCodes, opt => opt.MapFrom(src => src.CompanyCodeList))
                .ForMember(dest => dest.FileUploadIds, opt => opt.MapFrom(src => src.FileUploadIdList))
                .ReverseMap()
                .ForMember(dest => dest.CompanyIdList, opt => opt.MapFrom(src => src.CompanyIds))
                .ForMember(dest => dest.CompanyCodeList, opt => opt.MapFrom(src => src.CompanyCodes))
                .ForMember(dest => dest.FileUploadIdList, opt => opt.MapFrom(src => src.FileUploadIds));

            CreateMap<Data.Model.EmailConfig, Biz.Model.EmailConfig.View>()
                .ForMember(dest => dest.CompanyIds, opt => opt.MapFrom(src => src.CompanyIdList))
                .ForMember(dest => dest.CompanyCodes, opt => opt.MapFrom(src => src.CompanyCodeList))
                .ForMember(dest => dest.FileUploadIds, opt => opt.MapFrom(src => src.FileUploadIdList))
                .ReverseMap()
                .ForMember(dest => dest.CompanyIdList, opt => opt.MapFrom(src => src.CompanyIds))
                .ForMember(dest => dest.CompanyCodeList, opt => opt.MapFrom(src => src.CompanyCodes))
                .ForMember(dest => dest.FileUploadIdList, opt => opt.MapFrom(src => src.FileUploadIds));

            CreateMap<Data.Model.EmailConfig, Biz.Model.EmailConfig.List>().ReverseMap();
            CreateMap<Data.Model.EmailConfig, Biz.Model.EmailConfig.ShortInfo>().ReverseMap();

            CreateMap<Data.Model.SmsConfig, Biz.Model.SmsConfig.New>().ReverseMap();
            CreateMap<Data.Model.SmsConfig, Biz.Model.SmsConfig.Edit>().ReverseMap();
            CreateMap<Data.Model.SmsConfig, Biz.Model.SmsConfig.View>().ReverseMap();
            CreateMap<Data.Model.SmsConfig, Biz.Model.SmsConfig.List>().ReverseMap();



        }
        public static IMapper Mapper()
        {
            MapperConfiguration mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new MappingProfile());
            });
            return mappingConfig.CreateMapper();
        }
    }
}
