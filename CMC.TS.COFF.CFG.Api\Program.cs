﻿using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Api.Swagger;
using CMC.TS.COFF.Helper.Cache.Extensions;
using CMC.TS.COFF.Helper.Jwt;
using CMC.TS.COFF.Helper.RabbitMQ.Extension;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Serialization;
using Serilog;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// nccong: Add logger
Log.Logger = new LoggerConfiguration().CreateBootstrapLogger();
builder.Host.UseSerilog((ctx, lc) => lc
        .ReadFrom.Configuration(ctx.Configuration));
// nccong: Add CORS
string AllowOrigins = "TrustedOrigins";
string allowedHosts = builder.Configuration.GetValue<string>("AllowedHosts") ?? "*";
builder.Services.AddCors(options =>
{
    options.AddPolicy(AllowOrigins,
    builder =>
    {
        builder.WithOrigins(allowedHosts).AllowAnyHeader().AllowAnyMethod();
    });
});
//nccong: inject user context
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ITenantService, TenantService>();
// nccong: DB Config
string dbProvider = (builder.Configuration["DatabaseOptions:Provider"] ?? "SQLServer").ToLower();
switch (dbProvider)
{
    case "sqlserver":
        builder.Services.AddDbContext<DomainDbContext, SQLServerDbContext>();
        break;
    case "oracle":
        builder.Services.AddDbContext<DomainDbContext, OracleDbContext>();
        break;
    case "postgresql":
        builder.Services.AddDbContext<DomainDbContext, PostgreDbContext>();
        break;
    case "mysql":
        builder.Services.AddDbContext<DomainDbContext, MySqlDbContext>();
        break;
    case "sqlite":
        builder.Services.AddDbContext<DomainDbContext, SqLiteDbContext>();
        break;
    default:
        throw new Exception($"Unsupported provider: {dbProvider}");
}
// nccong: Add JwtToken
builder.Services.AddJwtAuthentication(builder.Configuration);
// Add services to the container.
// nccong: Replace
builder.Services.AddControllers().AddNewtonsoftJson(options =>
{
    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
    options.SerializerSettings.DateFormatString = "yyyy-MM-ddTHH:mm:ssZ";
});
// builder.Services.AddControllers();
// nccong: Redis cache
builder.Services.AddDistributedCache(options =>
{
    options.Host = builder.Configuration.GetValue<string>("Redis:Host");
    options.Instance = builder.Configuration.GetValue<string>("Redis:Instance");
});
// nccong: RabbitMQ
builder.Services.AddRabbitMQ(builder.Configuration, Assembly.GetExecutingAssembly());
builder.Services.AddScoped<CMC.TS.COFF.CFG.Biz.IRepositoryWrapper, CMC.TS.COFF.CFG.Biz.RepositoryWrapper>();
// nccong: Domain Service
//builder.Services.AddScoped<CMC.TS.COFF.CFG.Biz.IRepositoryWrapper>(provider => {
//    var context = provider.GetRequiredService<CMC.TS.COFF.CFG.Data.DomainDbContext>();
//    var logger = provider.GetRequiredService<ILogger<CMC.TS.COFF.CFG.Biz.RepositoryWrapper>>();
//    var configuration = provider.GetRequiredService<IConfiguration>();
//    var cacheService = provider.GetRequiredService<CMC.TS.COFF.Helper.Cache.Services.IDistributedCacheService>();
//    var tenantService = provider.GetRequiredService<CMC.TS.COFF.Helper.Tenant.ITenantService>();
//    var organizationService = provider.GetRequiredService<CMC.TS.COFF.CFG.Biz.Services.IOrganizationService>();
//    var httpContextAccessor = provider.GetRequiredService<IHttpContextAccessor>();
//    return new CMC.TS.COFF.CFG.Biz.RepositoryWrapper(
//        context, logger, configuration, cacheService, tenantService, organizationService, httpContextAccessor);
//});
// File Upload Service

// AutoMapper
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
// nccong: Replace
builder.Services.AddSwagger(builder.Configuration);
//builder.Services.AddSwaggerGen();

var app = builder.Build();

// nccong: Replace
// Configure the HTTP request pipeline.
app.UseSwagger(builder.Configuration);
//if (app.Environment.IsDevelopment())
//{
//    app.UseSwagger();
//    app.UseSwaggerUI();
//}

// nccong: Use CORS
app.UseCors(AllowOrigins);

// nccong: Use Authen
app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

// nccong: migration
try
{
    Log.Logger.Information($"Migration data.");
    using (var scope = app.Services.CreateScope())
    {
        using (var context = scope.ServiceProvider.GetRequiredService<DomainDbContext>())
        {
            context.Database.Migrate();
            // init data here
        }
    }
}
catch (Exception ex)
{
    Log.Logger.Fatal(ex, $"Đã có lỗi xảy ra trong quá trình migration dữ liệu.");
    throw;
}

app.Run();
