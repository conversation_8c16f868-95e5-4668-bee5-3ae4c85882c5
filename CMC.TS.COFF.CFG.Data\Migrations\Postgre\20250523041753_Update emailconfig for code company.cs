﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class Updateemailconfigforcodecompany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "UK_SmsConfigID_VariableKey",
                table: "SmsNotificationVariable");

            migrationBuilder.DropIndex(
                name: "UK_EmailConfigID_VariableKey",
                table: "NotificationVariable");

            migrationBuilder.DropColumn(
                name: "CREATED_AT",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "IS_EDITED",
                table: "EmailConfig");

            migrationBuilder.DropColumn(
                name: "UPDATED_AT",
                table: "EmailConfig");

            migrationBuilder.AddColumn<string>(
                name: "COMPANY_CODES",
                table: "EmailConfig",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_ID",
                table: "SmsNotificationVariable",
                column: "SMS_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_NOTIFICATION_VARIABLE_EMAIL_CONFIG_ID",
                table: "NotificationVariable",
                column: "EMAIL_CONFIG_ID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_ID",
                table: "SmsNotificationVariable");

            migrationBuilder.DropIndex(
                name: "IX_NOTIFICATION_VARIABLE_EMAIL_CONFIG_ID",
                table: "NotificationVariable");

            migrationBuilder.DropColumn(
                name: "COMPANY_CODES",
                table: "EmailConfig");

            migrationBuilder.AddColumn<DateTime>(
                name: "CREATED_AT",
                table: "EmailConfig",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IS_EDITED",
                table: "EmailConfig",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UPDATED_AT",
                table: "EmailConfig",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "UK_SmsConfigID_VariableKey",
                table: "SmsNotificationVariable",
                columns: new[] { "SMS_CONFIG_ID", "VARIABLE_KEY" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "UK_EmailConfigID_VariableKey",
                table: "NotificationVariable",
                columns: new[] { "EMAIL_CONFIG_ID", "VARIABLE_KEY" },
                unique: true);
        }
    }
}
