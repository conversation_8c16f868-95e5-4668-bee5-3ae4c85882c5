﻿using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.Helper.Biz;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConfigParameterController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<ConfigParameterController> _logger;
        private readonly IConfiguration _configuration;

        public ConfigParameterController(IRepositoryWrapper repository, ILogger<ConfigParameterController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Tạo mới cấu hình hệ thống
        /// Authorized role admin hoặc orgadmin
        /// </summary>
        /// <param name="model">Model nghiệp vụ Biz.New của ConfigParameter</param>
        /// <returns>Biz.Model.ConfigParameter.View nếu thành công</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] Biz.Model.ConfigParameter.New model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ConfigParameter.CreateAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        ///// <summary>
        ///// Cập nhật cấu hình hệ thống
        ///// Authorized role admin hoặc orgadmin
        ///// </summary>
        ///// <param name="model">Model nghiệp vụ Biz.Edit của ConfigParameter</param>
        ///// <returns>Biz.Model.ConfigParameter.View nếu thành công</returns>
        //[HttpPut]
        //public async Task<IActionResult> UpdateAsync([FromBody] Biz.Model.ConfigParameter.Edit model)
        //{
        //    _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
        //    try
        //    {
        //        if (ModelState.IsValid)
        //        {
        //            return await _repository.ConfigParameter.UpdateAsync(HttpContext, model);
        //        }
        //        return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
        //        return ResponseMessageWrapper.BuildErrorResponse(ex);
        //    }
        //}

        [HttpPut]
        public async Task<IActionResult> UpdateAsync([FromBody] Biz.Model.ConfigParameter.Edit model)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");

            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ConfigParameter.UpdateAsync(HttpContext, model);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Xóa cấu hình hệ thống
        /// Authorized role admin hoặc orgadmin
        /// </summary>
        /// <param name="id">Id của ConfigParameter</param>
        /// <returns>Thông báo thành công nếu xóa thành công</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAsync(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ConfigParameter.
                       DeleteAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy thông tin chi tiết của ConfigParameter theo id
        /// Authorized role admin hoặc orgadmin
        /// </summary>
        /// <param name="id">Id của ConfigParameter</param>
        /// <returns>Biz.Model.ConfigParameter.View</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetAsync(string id)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ConfigParameter.GetAsync(HttpContext, id);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy danh sách cấu hình hệ thống với phân trang và lọc
        /// Authorized role admin hoặc orgadmin
        /// </summary>
        /// <param name="filter">Model nghiệp vụ filter ConfigParameter</param>
        /// <returns>Danh sách phân trang ConfigParameter</returns>
        [HttpGet("list")]
        public async Task<IActionResult> GetListAsync([FromQuery] Biz.Model.ConfigParameter.Filter filter)
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ConfigParameter.GetAsync(HttpContext, filter);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }


        [HttpGet("latest")]
        public async Task<IActionResult> GetLatestAsync()
        {
            _logger.LogInformation($"Start {MethodBase.GetCurrentMethod()?.Name}");
            try
            {
                if (ModelState.IsValid)
                {
                    return await _repository.ConfigParameter.GetLatestAsync(HttpContext);
                }
                return ResponseMessageWrapper.BuildBadRequest(Message.MODEL_INVALID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{MethodBase.GetCurrentMethod()?.Name} error: {ex.Message}");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
    }
}