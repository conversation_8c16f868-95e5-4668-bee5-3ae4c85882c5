﻿using CMC.TS.COFF.Helper.Model;

namespace CMC.TS.COFF.CFG.Biz.Model.ConfigParameter
{
    public class Filter : PagingRequest
    {
        public string? RequireDepartmentSelection { get; set; }
        public string? UseEkyc { get; set; }
        public string? UseContractTemplate { get; set; }
        public string? UseContractType { get; set; }
        public string? EmailNotification { get; set; }
        public string? UseDepartmentOnContractCreation { get; set; }
        public string? SelectSignersByCompany { get; set; }
        public string UserId { get; set; } = string.Empty;
    }
}