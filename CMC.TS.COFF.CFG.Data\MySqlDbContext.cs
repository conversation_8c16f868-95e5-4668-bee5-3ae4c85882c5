﻿using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CMC.TS.COFF.CFG.Data
{
    public class MySqlDbContext : DomainDbContext
    {
        public MySqlDbContext(IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ILoggerFactory loggerFactory, ITenantService tenantService) : base(configuration, httpContextAccessor, loggerFactory, tenantService) { }
        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options.UseMySQL(_TenantService.ConnectionString).UseUpperSnakeCaseNamingConvention();
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }
    }
}
