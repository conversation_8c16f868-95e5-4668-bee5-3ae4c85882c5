﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBsmsconfigforcodesms : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MESSAGE_TYPE",
                table: "SmsConfig");

            migrationBuilder.AddColumn<string>(
                name: "CODE",
                table: "SmsConfig",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CODE",
                table: "SmsConfig");

            migrationBuilder.AddColumn<int>(
                name: "MESSAGE_TYPE",
                table: "SmsConfig",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }
    }
}
