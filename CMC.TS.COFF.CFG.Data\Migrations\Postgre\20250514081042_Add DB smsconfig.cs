﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBsmsconfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
          

            migrationBuilder.DropColumn(
                name: "ACTION_TYPE",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "IS_VALID",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "SUBJECT",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "WORKFLOW_DEF_ID",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "WORKFLOW_DEF_NAME",
                table: "SmsConfig");

            migrationBuilder.RenameColumn(
                name: "OBJECT_TYPE",
                table: "SmsConfig",
                newName: "MESSAGE_TYPE");

            migrationBuilder.RenameColumn(
                name: "CONTENT",
                table: "SmsConfig",
                newName: "CONTENT_VI");

            migrationBuilder.AddColumn<string>(
                name: "COMPANIES",
                table: "SmsConfig",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CONTENT_EN",
                table: "SmsConfig",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CREATED_AT",
                table: "SmsConfig",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "UPDATED_AT",
                table: "SmsConfig",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "COMPANIES",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "CONTENT_EN",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "CREATED_AT",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "UPDATED_AT",
                table: "SmsConfig");

            migrationBuilder.RenameColumn(
                name: "MESSAGE_TYPE",
                table: "SmsConfig",
                newName: "OBJECT_TYPE");

            migrationBuilder.RenameColumn(
                name: "CONTENT_VI",
                table: "SmsConfig",
                newName: "CONTENT");

            migrationBuilder.AddColumn<int>(
                name: "ACTION_TYPE",
                table: "SmsConfig",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "IS_VALID",
                table: "SmsConfig",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "SUBJECT",
                table: "SmsConfig",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "WORKFLOW_DEF_ID",
                table: "SmsConfig",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "WORKFLOW_DEF_NAME",
                table: "SmsConfig",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "SmsConfigExtend",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ACTION_NOTI = table.Column<int>(type: "integer", nullable: false),
                    ACTION_NOTI_NAME = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ACTION_TYPE = table.Column<long>(type: "bigint", nullable: false),
                    ACTION_TYPE_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CHECK_DUPLICATE_BY_WFS = table.Column<bool>(type: "boolean", nullable: false),
                    IS_REMIND_WORK = table.Column<bool>(type: "boolean", nullable: false),
                    OBJECT_TYPE = table.Column<int>(type: "integer", maxLength: 50, nullable: false),
                    OBJECT_TYPE_NAME = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    REMIND_WORK_DAY = table.Column<long>(type: "bigint", nullable: false),
                    WORKFLOW_DEF_ID = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    WORKFLOW_DEF_NAME = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SMS_CONFIG_EXTEND", x => x.ID);
                });
        }
    }
}
