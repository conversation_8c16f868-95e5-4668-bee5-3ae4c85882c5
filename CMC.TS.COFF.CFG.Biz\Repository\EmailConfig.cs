using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Biz.Model.EmailConfig;
using CMC.TS.COFF.CFG.Biz.Model.Organization;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.CFG.Data.Model;
using CMC.TS.COFF.Helper.Biz;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Extensions;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Models;
using CMC.TS.COFF.Helper.Tenant;
using Ganss.Xss;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Serilog.Core;
using System.Linq;
using System.Net.Http;

namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class EmailConfig : RepositoryBase<Data.Model.EmailConfig, string>, IEmailConfig
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public EmailConfig(
            DomainDbContext context,
            ILogger<RepositoryWrapper> logger,
            IMapper mapper,
            IConfiguration configuration,
            IDistributedCacheService cacheService,
            ITenantService tenantService,
            IHttpContextAccessor httpContextAccessor) : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<Model.EmailConfig.View> CreateAsync(Model.EmailConfig.New model)
        {
            _logger.LogInformation($"Creating EmailConfig for CategoryID: {model.EmailConfigCategoryID}, IsDefault: {model.IsDefault}");
            _logger.LogInformation($"CompanyIds count: {model.CompanyIds?.Count ?? 0}");
            _logger.LogInformation($"CompanyCodes count: {model.CompanyCodes?.Count ?? 0}");
            _logger.LogInformation($"FileUploadIds count: {model.FileUploadIds?.Count ?? 0}");

            var category = await _context.EmailConfigCategories.FindAsync(model.EmailConfigCategoryID);
            if (category == null)
            {
                throw new ArgumentException($"EmailConfigCategory with ID {model.EmailConfigCategoryID} does not exist");
            }

            if (model.CompanyIds == null || !model.CompanyIds.Any())
            {
                throw new ArgumentException("CompanyIds is required");
            }

            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    var sanitizer = new HtmlSanitizer();
                    sanitizer.AllowDataAttributes = true;
                    sanitizer.AllowedAttributes.Add("src");
                    sanitizer.AllowedSchemes.Add("data");

                    var emailConfig = _mapper.Map<Data.Model.EmailConfig>(model);

                    _logger.LogInformation($"Mapped EmailConfig - CompanyIds: '{emailConfig.CompanyIds}', CompanyCodes: '{emailConfig.CompanyCodes}', FileUploadIds: '{emailConfig.FileUploadIds}'");


                    if (model.IsDefault)
                    {
                        _logger.LogInformation($"Setting new EmailConfig as default for CategoryID: {model.EmailConfigCategoryID}");

                        var defaultConfigs = await _context.EmailConfigs
                            .Where(x => x.IsDefault && x.EmailConfigCategoryID == model.EmailConfigCategoryID)
                            .ToListAsync();

                        _logger.LogInformation($"Found {defaultConfigs.Count} existing default configs to update");

                        foreach (var config in defaultConfigs)
                        {
                            _logger.LogInformation($"Updating EmailConfig ID: {config.ID} to not default");
                            config.IsDefault = false;
                            _context.EmailConfigs.Update(config);
                        }
                    }

                    if (model.Content.Contains("data:image"))
                    {
                        emailConfig.Content = model.Content;
                    }
                    else
                    {
                        emailConfig.Content = sanitizer.Sanitize(model.Content);
                    }


                    _context.EmailConfigs.Add(emailConfig);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();

                    return _mapper.Map<Model.EmailConfig.View>(emailConfig);


                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, $"Create EmailConfig Error for CategoryID: {model.EmailConfigCategoryID}. Error: {ex.Message}");
                    _logger.LogError($"Stack trace: {ex.StackTrace}");
                    throw; // Re-throw để controller có thể handle
                }
            }
        }


        public async Task<Model.EmailConfig.View> UpdateAsync(Model.EmailConfig.Edit model)
        {
            var category = await _context.EmailConfigCategories.FindAsync(model.EmailConfigCategoryID);
            if (category == null)
            {
                throw new ArgumentException($"EmailConfigCategory with ID {model.EmailConfigCategoryID} does not exist");
            }
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    var sanitizer = new HtmlSanitizer();
                    sanitizer.AllowDataAttributes = true;
                    sanitizer.AllowedAttributes.Add("src");
                    sanitizer.AllowedSchemes.Add("data");

                    var oldItem = await _context.EmailConfigs
                        .FirstOrDefaultAsync(e => e.ID == model.Id);

                    if (oldItem == null)
                        return null;


                    if (model.IsDefault)
                    {
                        _logger.LogInformation($"Setting EmailConfig ID: {model.Id} as default for CategoryID: {model.EmailConfigCategoryID}");

                        var defaultConfigs = await _context.EmailConfigs
                            .Where(x => x.IsDefault && x.EmailConfigCategoryID == model.EmailConfigCategoryID && x.ID != model.Id)
                            .ToListAsync();

                        _logger.LogInformation($"Found {defaultConfigs.Count} existing default configs to update");

                        foreach (var config in defaultConfigs)
                        {
                            _logger.LogInformation($"Updating EmailConfig ID: {config.ID} to not default");
                            config.IsDefault = false;
                            _context.EmailConfigs.Update(config);
                        }
                    }

                    _mapper.Map(model, oldItem);

                    _context.EmailConfigs.Update(oldItem);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();


                    return _mapper.Map<Model.EmailConfig.View>(oldItem);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, $"Update EmailConfig Error for ID: {model.Id}, CategoryID: {model.EmailConfigCategoryID}. Error: {ex.Message}");
                    _logger.LogError($"Stack trace: {ex.StackTrace}");
                    throw; // Re-throw để controller có thể handle
                }
            }
        }

        public async Task<Model.EmailConfig.View> GetAsync(HttpContext httpContext, long id)
        {
            var emailConfig = await _context.EmailConfigs
            .FirstOrDefaultAsync(e => e.ID == id);

            if (emailConfig == null)
                return null;
            UserInfo currentUser = httpContext.UserContext();
            List<OrganizationInfos> organizationInfos = new List<OrganizationInfos>();
            ApiClient apiClient = new ApiClient();

            var apiResult = await apiClient.GetAsync($"{_configuration["API_GATEWAY"]}/ums/Organization/short-info", user: currentUser);
            if (apiResult != null && apiResult.Success)
            {
                if (apiResult.Data != null)
                {
                    organizationInfos = JsonConvert.DeserializeObject<List<OrganizationInfos>>(apiResult.Data.ToString());
                }
            }
            else
            {
                _logger.LogError($"Lấy thông tin Company thất bại: {apiResult?.Message}");

            }

            try
            {
                var result = _mapper.Map<Model.EmailConfig.View>(emailConfig);
                result.Companies = organizationInfos.Where(o => result.CompanyIds.Contains(o.Id)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting EmailConfig {id}");
                return null;
            }
        }

        public async Task<Model.EmailConfig.ShortInfo?> GetByEmailCategoryAndOrgCodeAsync(HttpContext httpContext, string? orgCode, string code)
        {
            _logger.LogInformation($"GetByEmailCategoryAndOrgCodeAsync - Looking for category: {code}, orgCode: {orgCode}");

            var category = _context.EmailConfigCategories.FirstOrDefault(x => x.Code.Equals(code));
            if (category == null)
            {
                _logger.LogInformation($"GetByEmailCategoryAndOrgCodeAsync not found category: {code}");
                return null;
            }

            var configs = _context.EmailConfigs.Where(x => x.EmailConfigCategoryID == category.ID).ToList();
            _logger.LogInformation($"Found {configs.Count} configs for category {code}");

            Data.Model.EmailConfig? config = null;
            if (!string.IsNullOrEmpty(orgCode))
            {
                foreach (var cfg in configs)
                {
                    var companyCodes = cfg.CompanyCodeList;
                    _logger.LogInformation($"Config ID: {cfg.ID}, CompanyCodes: [{string.Join(", ", companyCodes)}]");
                }

                config = configs.FirstOrDefault(x => x.CompanyCodeList.Contains(orgCode));
                if (config != null)
                {
                    _logger.LogInformation($"Found matching config ID: {config.ID} for orgCode: {orgCode}");
                }
                else
                {
                    _logger.LogInformation($"No config found for orgCode: {orgCode}, will try default");
                }
            }
            if (config == null)
            {
                config = configs.FirstOrDefault(x => x.IsDefault);
                if (config != null)
                {
                    _logger.LogInformation($"Using default config ID: {config.ID} for category: {code}");
                }
            }

            if (config == null)
            {
                _logger.LogInformation($"GetByEmailCategoryAndOrgCodeAsync not found config email for category: {code}");
                return null;
            }

            _logger.LogInformation($"Returning config ID: {config.ID} for category: {code}, orgCode: {orgCode}");
            return _mapper.Map<Data.Model.EmailConfig, Biz.Model.EmailConfig.ShortInfo>(config);

        }


        public async Task<List<Model.EmailConfig.View>> GetAllAsync()
        {
            var emailConfigs = await _context.EmailConfigs
                .ToListAsync();

            var result = _mapper.Map<List<Data.Model.EmailConfig>, List<Model.EmailConfig.View>>(emailConfigs);


            return result;
        }

        public async Task MemoryStreamToEntities(byte[] content)
        {
            var deserialized = ProtoBufHelper.Deserialize<IEnumerable<Model.EmailConfig.View>>(content);
            var entities = _mapper.Map<List<Data.Model.EmailConfig>>(deserialized);

            var entitiesCurrent = await _context.EmailConfigs.ToListAsync();

            var dataAdd = entities.Where(m => !entitiesCurrent.Select(mm => mm.ID).Contains(m.ID)).ToList();
            var dataUpdate = entities.Where(m => entitiesCurrent.Select(mm => mm.ID).Contains(m.ID)).ToList();
            var dataDelete = entitiesCurrent.Where(m => !entities.Select(mm => mm.ID).Contains(m.ID)).ToList();

            if (dataAdd?.Any() == true)
            {
                foreach (var item in dataAdd)
                {
                    _context.EmailConfigs.Add(item);
                }
            }

            if (dataUpdate?.Any() == true)
            {
                foreach (var item in dataUpdate)
                {
                    var oldItem = entitiesCurrent.FirstOrDefault(e => e.ID == item.ID);
                    if (oldItem != null)
                    {

                        oldItem.IsValid = item.IsValid;
                        oldItem.Subject = item.Subject;
                        oldItem.Content = item.Content;
                        _context.EmailConfigs.Update(oldItem);
                    }
                }
            }

            if (dataDelete?.Any() == true)
            {
                _context.EmailConfigs.RemoveRange(dataDelete);
            }

            await _context.SaveChangesAsync();
        }

        public async Task<byte[]> EntitiesToMemoryStream()
        {
            var entities = await _context.EmailConfigs
                .Select(m => _mapper.Map<Data.Model.EmailConfig, Model.EmailConfig.View>(m))
                .ToListAsync();

            return ProtoBufHelper.Serialize(entities);
        }

        public override Task<PagedResult<Data.Model.EmailConfig>> GetItemsAsync(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public override Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public Task<Dictionary<string, string>> GetCompanyVariablesAsync(long companyId)
        {
            throw new NotImplementedException();
        }

        public Task<View> GetByCodeAsync(string code)
        {
            throw new NotImplementedException();
        }

        public async Task<List<Model.EmailConfig.View>> GetByEmailConfigCategoryIdAsync(HttpContext httpContext, long emailConfigCategoryId)
        {
            var emailConfigs = await _context.EmailConfigs
                .Where(e => e.EmailConfigCategoryID == emailConfigCategoryId)
                .ToListAsync();


            UserInfo currentUser = httpContext.UserContext();
            List<OrganizationInfos> organizationInfos = new List<OrganizationInfos>();
            ApiClient apiClient = new ApiClient();

            var apiResult = await apiClient.GetAsync($"{_configuration["API_GATEWAY"]}/ums/Organization/short-info", user: currentUser);
            if (apiResult != null && apiResult.Success)
            {
                if (apiResult.Data != null)
                {
                    organizationInfos = JsonConvert.DeserializeObject<List<OrganizationInfos>>(apiResult.Data.ToString());
                }
            }
            else
            {
                _logger.LogError($"Lấy thông tin Company thất bại: {apiResult?.Message}");

            }

            if (emailConfigs == null || !emailConfigs.Any())
                return new List<Model.EmailConfig.View>();

            var result = _mapper.Map<List<Model.EmailConfig.View>>(emailConfigs);

            foreach (var email in result)
            {
                if (email.CompanyIds != null && email.CompanyIds.Any())
                {
                    email.Companies = organizationInfos
                        .Where(o => email.CompanyIds.Contains(o.Id))
                        .ToList();
                }
                else
                {
                    email.Companies = new List<OrganizationInfos>();
                }
            }

            return result;

        }


        public async Task<bool> DeleteAsync(long id)
        {
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                bool isDefault = false;
                try
                {
                    _logger.LogInformation($"Bắt đầu xóa EmailConfig với ID = {id}");

                    var emailConfig = await _context.EmailConfigs
                        .FirstOrDefaultAsync(e => e.ID == id);

                    if (emailConfig == null)
                    {
                        _logger.LogWarning($"Không tìm thấy EmailConfig với ID = {id}");
                        return false;
                    }

                    // Kiểm tra nếu là mẫu mặc định thì không cho xóa
                    if (emailConfig.IsDefault)
                    {
                        isDefault = true;
                        _logger.LogWarning($"EmailConfig với ID = {id} là mặc định, không được phép xóa");
                        throw new InvalidOperationException(Message.DELETE_IS_DEFAULT);
                    }


                    // Xóa EmailConfig
                    _logger.LogInformation($"Đang xóa EmailConfig với ID = {id}");
                    _context.EmailConfigs.Remove(emailConfig);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();
                    _logger.LogInformation($"Đã xóa thành công EmailConfig với ID = {id}");

                    return true;
                }
                catch (Exception ex)
                {
                    if (isDefault)
                    {
                        throw new InvalidOperationException(Message.DELETE_IS_DEFAULT);
                    }

                    try
                    {
                        await transaction.RollbackAsync();
                    }
                    catch (Exception rollbackEx)
                    {
                        _logger.LogError(rollbackEx, $"Lỗi khi rollback transaction khi xóa EmailConfig {id}");
                    }

                    _logger.LogError(ex, $"Lỗi khi xóa EmailConfig {id}: {ex.Message}");
                    return false;
                }
            }
        }

        public Task<View> GetAsync(long id)
        {
            throw new NotImplementedException();
        }

        public Task<List<View>> GetByEmailConfigCategoryIdAsync(long emailConfigCategoryId)
        {
            throw new NotImplementedException();
        }

        public static string ConvertListToCommaSeparatedString(List<string> input)
        {
            if (input == null || input.Count == 0)
                return string.Empty;

            return string.Join(", ", input
                .Where(s => !string.IsNullOrWhiteSpace(s))
                .Select(s => s.Trim()));
        }


        public static List<string> ConvertCommaSeparatedStringToList(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return new List<string>();

            return input
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();
        }

        public Task<Dictionary<string, string>> GetNotificationVariablesAsync(long emailConfigId)
        {
            throw new NotImplementedException();
        }
    }
}
