﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CMC.TS.COFF.Helper.Model;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Table("EmailConfigCategory")]
    public class EmailConfigCategory : TrackingSystem
    {
        [Key]
        public long ID { get; set; }

        [Required]
        [StringLength(255)]
        [Column("CODE")]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Column("NAME")]
        public string Name { get; set; } = string.Empty;

        [Column("IS_VALID")]
        public bool IsValid { get; set; } = true;

        [Required]
        [Column("IS_DEFAULT")]
        public bool IsDefault { get; set; } = false;

        [StringLength(500)]
        [Column("DESCRIPTION")]
        public string? Description { get; set; }

        public virtual ICollection<EmailConfig> EmailConfigs { get; set; } = new List<EmailConfig>();
    }
}