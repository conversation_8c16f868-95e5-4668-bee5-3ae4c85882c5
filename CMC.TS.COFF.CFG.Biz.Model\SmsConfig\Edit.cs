﻿using CMC.TS.COFF.CFG.Enums;
using CMC.TS.COFF.Helper.Model;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CMC.TS.COFF.CFG.Biz.Model.SmsConfig
{
    public class Edit : TEditModelBase<string>
    {

        [JsonPropertyName("id")]
        public long ID { get; set; }

        [JsonPropertyName("code")]
        public string Code { get; set; }


        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("companyIds")]
        public List<string> CompanyIds { get; set; }

        [JsonPropertyName("contentVi")]
        public string ContentVi { get; set; }

        [JsonPropertyName("contentEn")]
        public string ContentEn { get; set; }

        [JsonPropertyName("companies")]
        public List<Model.Organization.OrganizationInfos> Companies { get; set; } = new List<Model.Organization.OrganizationInfos>();
    }
}