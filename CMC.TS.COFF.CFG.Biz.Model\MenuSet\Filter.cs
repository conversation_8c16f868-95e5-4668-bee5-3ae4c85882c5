﻿using CMC.TS.COFF.Helper.Model;

namespace CMC.TS.COFF.CFG.Biz.Model.MenuSet
{
    public class Filter : PagingRequest
    {
        private string? _Code;
        public string? Code
        {
            get
            {
                return _Code;
            }
            set
            {
                _Code = value;
                NormalizedCode = value?.ToUpperInvariant();
            }
        }
        public string? NormalizedCode { get; set; }
        private string? _ExactlyCode;
        public string? ExactlyCode
        {
            get
            {
                return _ExactlyCode;
            }
            set
            {
                _ExactlyCode = value;
                NormalizedExactlyCode = value?.ToUpperInvariant();
            }
        }
        public string? NormalizedExactlyCode { get; set; }
        private string? _Name;
        public string? Name
        {
            get
            {
                return _Name;
            }
            set
            {
                _Name = value;
                NormalizedName = value?.ToUpperInvariant();
            }
        }
        public string? NormalizedName { get; set; }
        public bool? IsDefault { get; set; }
    }

    public class RemoveModel
    {
        public string MenusetId { get; set; } = string.Empty;
        public string? ParentId { get; set; }
        public string ItemId { get; set; } = string.Empty;
    }

    public class FilterDefs: PagingRequest
    {
        public string? ParentId { get; set; }
        public string MenuSetId { get; set; } = string.Empty;
    }
}
