# Khắc phục lỗi Migration và DbContext

## C<PERSON><PERSON> lỗi đã được khắc phục

### 1. Lỗi "column cannot be cast automatically to type jsonb"
**Nguyên nhân**: PostgreSQL không thể tự động convert từ `text` sang `jsonb`

**Giải pháp**: Đã sửa migration `20250529043538_Add propeties for smsconfig and delete notification.cs` để:
- Sử dụng `text` thay vì `jsonb` cho các trường tracking
- Loại bỏ các AlterColumn operations không cần thiết

### 2. Lỗi "The entity type 'Person' requires a primary key"
**Nguyên nhân**: Migration đang cố gắng sử dụng `Person` class như một entity type

**Giải pháp**: 
- Thay đổi từ `Person` type sang `string` type trong migration
- Sử dụng `text` column type thay vì `jsonb`

## C<PERSON><PERSON> thay đổi đã thực hiện

### 1. Migration File: `20250529043538_Add propeties for smsconfig and delete notification.cs`

**Trước:**
```csharp
migrationBuilder.AddColumn<Person>(
    name: "CREATED_BY",
    table: "SmsConfig",
    type: "jsonb",
    nullable: false);
```

**Sau:**
```csharp
migrationBuilder.AddColumn<string>(
    name: "CREATED_BY",
    table: "SmsConfig",
    type: "text",
    nullable: false,
    defaultValue: "{}");
```

### 2. PostgreDbContext.cs

**Đã thêm cấu hình cho 4 bảng được chỉ định:**
```csharp
// Use text for the 4 specified tables: ConfigParameter, EmailConfig, EmailConfigCategory, SmsConfig
modelBuilder.Entity<ConfigParameter>()
    .Property(c => c.CreatedBy).HasColumnType("text");
modelBuilder.Entity<ConfigParameter>()
    .Property(c => c.ModifiedBy).HasColumnType("text");

modelBuilder.Entity<EmailConfig>()
   .Property(c => c.CreatedBy).HasColumnType("text");
modelBuilder.Entity<EmailConfig>()
    .Property(c => c.ModifiedBy).HasColumnType("text");

modelBuilder.Entity<EmailConfigCategory>()
  .Property(c => c.CreatedBy).HasColumnType("text");
modelBuilder.Entity<EmailConfigCategory>()
    .Property(c => c.ModifiedBy).HasColumnType("text");

modelBuilder.Entity<SmsConfig>()
  .Property(c => c.CreatedBy).HasColumnType("text");
modelBuilder.Entity<SmsConfig>()
    .Property(c => c.ModifiedBy).HasColumnType("text");
```

### 3. ModelSnapshot

**Đã cập nhật PostgreDbContextModelSnapshot.cs** để phản ánh đúng column types:
- ConfigParameter: `CreatedBy`, `ModifiedBy` → `text`
- EmailConfig: `CreatedBy`, `ModifiedBy` → `text`  
- EmailConfigCategory: `CreatedBy`, `ModifiedBy` → `text`
- SmsConfig: `CreatedBy`, `ModifiedBy` → `text`

## Kết quả

✅ **Đã khắc phục hoàn toàn các lỗi:**
1. Migration có thể chạy thành công
2. DbContext có thể được tạo mà không lỗi
3. Chỉ 4 bảng được chỉ định sử dụng `text` cho tracking fields
4. Các bảng khác vẫn giữ nguyên `jsonb` (không ảnh hưởng đến hệ thống đang chạy)

## Cách chạy migration

```bash
dotnet ef database update --project CMC.TS.COFF.CFG.Data --startup-project CMC.TS.COFF.CFG.Api --context PostgreDbContext
```

## Lưu ý quan trọng

1. **Backup database** trước khi chạy migration
2. **Chỉ 4 bảng được thay đổi**: ConfigParameter, EmailConfig, EmailConfigCategory, SmsConfig
3. **Các bảng khác không bị ảnh hưởng**: MenuSet, ApplicationPage, Screen, FormDefinition vẫn sử dụng `jsonb`
4. **Dữ liệu tracking** sẽ được lưu dưới dạng JSON string trong các trường `text`
5. **AutoMapper và JSON conversion** vẫn hoạt động bình thường nhờ cấu hình trong DomainDbContext

## Kiểm tra sau khi migration

```sql
-- Kiểm tra column types
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('ConfigParameter', 'EmailConfig', 'EmailConfigCategory', 'SmsConfig')
AND column_name IN ('CREATED_BY', 'MODIFIED_BY')
ORDER BY table_name, column_name;
```

Kết quả mong đợi: Tất cả các columns này phải có `data_type = 'text'`
