﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface IConfigParameter
    {
        Task<IActionResult> CreateAsync(HttpContext httpContext, Biz.Model.ConfigParameter.New model);
        Task<IActionResult> UpdateAsync(HttpContext httpContext, Biz.Model.ConfigParameter.Edit model);
        Task<IActionResult> DeleteAsync(HttpContext context, string id);
        Task<IActionResult> GetAsync(HttpContext httpContext, string id);
        Task<IActionResult> GetAsync(HttpContext context, Biz.Model.ConfigParameter.Filter model);
        Task<IActionResult> GetLatestAsync(HttpContext httpContext);
    }
}
