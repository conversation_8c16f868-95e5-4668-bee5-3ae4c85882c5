﻿using CMC.TS.COFF.CFG.Biz.Model.EmailConfig;
using CMC.TS.COFF.CFG.Biz.Model.Organization;
using CMC.TS.COFF.Helper.Model;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace CMC.TS.COFF.CFG.Biz.Model.EmailConfig
{
    [ProtoContract]
    public class View : TViewModelBase<string>
    {
        [ProtoMember(1)]
        public long ID { get; set; }

        [ProtoMember(2)]
        public long EmailConfigCategoryID { get; set; }

        [ProtoMember(5)]
        public bool IsValid { get; set; } = true;

        [ProtoMember(6)]
        public bool IsDefault { get; set; }

        [ProtoMember(7)]
        public bool IsEdited { get; set; }

        [ProtoMember(8)]
        public string Subject { get; set; } = string.Empty;

        [ProtoMember(9)]
        public string Content { get; set; } = string.Empty;

        [ProtoMember(10)]
        public List<string> CompanyIds { get; set; } = new List<string>();

        [ProtoMember(12)]
        public List<string> CompanyCodes { get; set; } = new List<string>();

        [ProtoMember(11)]
        public List<OrganizationInfos> Companies { get; set; } = new List<OrganizationInfos>();

        [ProtoMember(13)]
        public List<string> FileUploadIds { get; set; } = new List<string>();
    }

    public class ShortInfo
    {
        public long ID { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }
}