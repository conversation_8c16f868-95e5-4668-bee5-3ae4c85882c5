﻿using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Biz.Model.ApplicationPage;
using CMC.TS.COFF.CFG.Constant;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Biz;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class ApplicationPage : RepositoryBase<Data.Model.ApplicationPage, string>, IApplicationPage
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;
        public ApplicationPage(DomainDbContext context, ILogger<RepositoryWrapper> logger, IMapper mapper, IConfiguration configuration, IDistributedCacheService cacheService, ITenantService tenantService) : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
        }

        public async Task<IActionResult> CreateAsync(HttpContext httpContext, New model)
        {
            var isExist = _context.ApplicationPages!.Any(x => x.NormalizedCode.Equals(model.Code.ToUpperInvariant()));
            if (isExist)
            {
                _logger.LogInformation($"ApplicationPage.CreateAsync has code: {model.Code}");
                return ResponseMessageWrapper.BuildBadRequest(Message.EXIST_ITEM);
            }
            _logger.LogDebug("ApplicationPage.CreateAsync start mapper model");
            var item = _mapper.Map<Biz.Model.ApplicationPage.New, Data.Model.ApplicationPage>(model);
            _logger.LogDebug("ApplicationPage.CreateAsync start create ApplicationPage");
            item = await CreateItemAsync(item);
            _logger.LogInformation("ApplicationPage.CreateAsync create succeed");
            return ResponseMessageWrapper.BuildSuccess(item);
        }

        public async Task<IActionResult> UpdateAsync(HttpContext httpContext, Biz.Model.ApplicationPage.Edit model)
        {
            var oldItem = _context.ApplicationPages!.Any(x => x.Id.Equals(model.Id));
            if (!oldItem)
            {
                _logger.LogInformation($"ApplicationPage.UpdateAsync not found id: {model.Id}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            _logger.LogInformation($"ApplicationPage.UpdateAsync start update: {model.Id}");
            var item = await UpdateItemAsync<Biz.Model.ApplicationPage.Edit>(model);
            _logger.LogInformation($"ApplicationPage.UpdateAsync update: {model.Id} succeed");
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.ApplicationPage, Biz.Model.ApplicationPage.View>(item));
        }

        public async Task<IActionResult> DeleteAsync(HttpContext context, string id)
        {
            _logger.LogInformation($"ApplicationPage.DeleteAsync start delete: {id}");
            // Do cấu hình mặc định onDelete: ReferentialAction.Cascade nên khi xóa là sẽ xóa luôn các screen và form definition liên quan
            var application = _context.ApplicationPages!.First(x => x.Id.Equals(id));
            if (application == null)
            {
                _logger.LogInformation($"ApplicationPage.DeleteAsync not found: {id}");
                return ResponseMessageWrapper.BuildNotFound();
            }
            _context.ApplicationPages!.Remove(application);
            await _context.SaveChangesAsync();
            _logger.LogInformation($"ApplicationPage.DeleteAsync delete: {id} succeed");
            return ResponseMessageWrapper.BuildSuccess();
        }

        public async Task<IActionResult> GetAsync(HttpContext context, Biz.Model.ApplicationPage.Filter model)
        {
            var items = _context.ApplicationPages!.AsNoTracking()
                .Where(x => x.Status == Helper.Enums.ItemStatus.Active);
            if (!string.IsNullOrEmpty(model.NormalizedCode))
            {
                items = items.Where(x => x.NormalizedCode.Contains(model.NormalizedCode));
            }
            if (!string.IsNullOrEmpty(model.NormalizedExactlyCode))
            {
                items = items.Where(x => x.NormalizedCode.Equals(model.NormalizedExactlyCode));
            }
            if (!string.IsNullOrEmpty(model.NormalizedName))
            {
                items = items.Where(x => x.NormalizedName.Contains(model.NormalizedName));
            }
            return ResponseMessageWrapper.BuildSuccess(await items.OrderBy(x => x.SeqNo).GetPagedAsync<Data.Model.ApplicationPage, Biz.Model.ApplicationPage.List>(_mapper, model));
        }

        public async Task<IActionResult> GetAsync(HttpContext context, string id)
        {
            var item = await _context.ApplicationPages!.FindAsync(id);
            if (item == null)
            {
                _logger.LogInformation($"ApplicationPage.GetAsync not found id: {id}");
                return ResponseMessageWrapper.BuildNotFound(Constant.Message.NOT_FOUND);
            }
            return ResponseMessageWrapper.BuildSuccess(_mapper.Map<Data.Model.ApplicationPage, Model.ApplicationPage.View>(item));
        }

        public async Task<IActionResult> GetAsync(HttpContext context)
        {
            throw new NotImplementedException();
        }
        #region Base actions
        public override Task<PagedResult<Data.Model.ApplicationPage>> GetItemsAsync(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public override Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            var items = _context.ApplicationPages!.AsNoTracking().GetPagedAsync<Data.Model.ApplicationPage, TListModel>(_mapper, model);
            return items;
        }
        #endregion
    }
}
