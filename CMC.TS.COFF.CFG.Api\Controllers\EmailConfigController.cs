﻿using Microsoft.AspNetCore.Mvc;
using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.Helper.Biz;
using System.Reflection;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmailConfigController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<EmailConfigController> _logger;
        private readonly IConfiguration _configuration;
        private readonly CMC.TS.COFF.CFG.Data.DomainDbContext _context;

        public EmailConfigController(
            IRepositoryWrapper repository,
            ILogger<EmailConfigController> logger,
            IConfiguration configuration,
            CMC.TS.COFF.CFG.Data.DomainDbContext context)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
            _context = context;
        }

        [HttpGet("GetEmailById")]
        public async Task<IActionResult> GetEmailById(long id)
        {
            _logger.LogInformation($"Start GetEmailById {id}");
            try
            {
                var item = await _repository.EmailConfig.GetAsync(HttpContext, id);
                if (item == null)
                    return ResponseMessageWrapper.BuildNotFound();
                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Get EmailConfig {id} Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }


        [HttpPost("EmailConfig")]
        public async Task<IActionResult> CreateEmailConfig([FromBody] COFF.CFG.Biz.Model.EmailConfig.New model)
        {
            _logger.LogInformation($"Start PostEmailConfig");
            try
            {
                // Validate the model
                if (model == null)
                    return ResponseMessageWrapper.BuildBadRequest("Model cannot be null");

                // Kiểm tra xem EmailConfigCategoryID có hợp lệ không
                if (model.EmailConfigCategoryID <= 0)
                    return ResponseMessageWrapper.BuildBadRequest("EmailConfigCategoryID must be greater than 0");


                // Kiểm tra xem Subject và Content có được cung cấp không
                if (string.IsNullOrEmpty(model.Subject))
                    return ResponseMessageWrapper.BuildBadRequest("Subject is required");

                if (string.IsNullOrEmpty(model.Content))
                    return ResponseMessageWrapper.BuildBadRequest("Content is required");

                try
                {
                    // Kiểm tra xem EmailConfigCategory có tồn tại không
                    var category = await _context.EmailConfigCategories.FindAsync(model.EmailConfigCategoryID);
                    if (category == null)
                        return ResponseMessageWrapper.BuildBadRequest($"EmailConfigCategory with ID {model.EmailConfigCategoryID} does not exist");

                    var item = await _repository.EmailConfig.CreateAsync(model);
                    if (item == null)
                        return ResponseMessageWrapper.BuildErrorResponse(new Exception("Failed to create EmailConfig"));

                    return ResponseMessageWrapper.BuildSuccess(item);
                }
                catch (ArgumentException ex)
                {
                    return ResponseMessageWrapper.BuildBadRequest(ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Post EmailConfig Error: " + ex.Message);
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpPut("EmailConfig")]
        public async Task<IActionResult> PutEmailConfig([FromBody] COFF.CFG.Biz.Model.EmailConfig.Edit model)
        {
            _logger.LogInformation($"Start PutEmailConfig");
            try
            {
                // Validate the model
                if (model == null)
                    return ResponseMessageWrapper.BuildBadRequest("Model cannot be null");

                if (model.Id <= 0)
                    return ResponseMessageWrapper.BuildBadRequest("Invalid EmailConfig ID");

                // Kiểm tra xem EmailConfigCategoryID có hợp lệ không
                if (model.EmailConfigCategoryID <= 0)
                    return ResponseMessageWrapper.BuildBadRequest("EmailConfigCategoryID must be greater than 0");

                // Kiểm tra xem Subject và Content có được cung cấp không
                if (string.IsNullOrEmpty(model.Subject))
                    return ResponseMessageWrapper.BuildBadRequest("Subject is required");

                if (string.IsNullOrEmpty(model.Content))
                    return ResponseMessageWrapper.BuildBadRequest("Content is required");

                

                try
                {
                    // Kiểm tra xem EmailConfigCategory có tồn tại không
                    var category = await _context.EmailConfigCategories.FindAsync(model.EmailConfigCategoryID);
                    if (category == null)
                        return ResponseMessageWrapper.BuildBadRequest($"EmailConfigCategory with ID {model.EmailConfigCategoryID} does not exist");

                    var item = await _repository.EmailConfig.UpdateAsync(model);
                    if (item == null)
                        return ResponseMessageWrapper.BuildErrorResponse(new Exception("Failed to update EmailConfig"));

                    return ResponseMessageWrapper.BuildSuccess(item);
                }
                catch (ArgumentException ex)
                {
                    return ResponseMessageWrapper.BuildBadRequest(ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Put EmailConfig Error: " + ex.Message);
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpGet("GetByCategory/{emailConfigCategoryId}")]
        public async Task<IActionResult> GetByEmailConfigCategoryId(long emailConfigCategoryId)
        {
            _logger.LogInformation($"Start GetByEmailConfigCategoryId {emailConfigCategoryId}");
            try
            {
                var emailConfigs = await _repository.EmailConfig.GetByEmailConfigCategoryIdAsync(HttpContext, emailConfigCategoryId);
                if (emailConfigs == null || !emailConfigs.Any())
                    return ResponseMessageWrapper.BuildNotFound();
                return ResponseMessageWrapper.BuildSuccess(emailConfigs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Get EmailConfigs by CategoryId {emailConfigCategoryId} Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            _logger.LogInformation($"Start Delete EmailConfig {id}");
            try
            {
                var result = await _repository.EmailConfig.DeleteAsync(id);
                if (!result)
                    return ResponseMessageWrapper.BuildNotFound("Email config not found");

                return ResponseMessageWrapper.BuildSuccess(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Delete EmailConfig {id} error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        [HttpGet("GetByCategoryAndOrgCode")]
        public async Task<IActionResult> getByOrgCodeAndEmailConfigCategory(string? orgCode = "", string code = "")
        {
            _logger.LogInformation($"Start get ByCateogry EmailConfig {orgCode}");
            try
            {
                var result = await _repository.EmailConfig.GetByEmailCategoryAndOrgCodeAsync(HttpContext, orgCode, code);
                if (result == null)
                    return ResponseMessageWrapper.BuildNotFound("Email config not found");

                return ResponseMessageWrapper.BuildSuccess(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Delete EmailConfigCategory {code} and Code Company: {orgCode} and error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
    }
}