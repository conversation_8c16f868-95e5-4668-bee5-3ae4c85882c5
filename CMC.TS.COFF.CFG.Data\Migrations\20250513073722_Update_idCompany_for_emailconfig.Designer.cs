// <auto-generated />
using System;
using CMC.TS.COFF.CFG.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace CMC.TS.COFF.CFG.Data.Migrations
{
    [DbContext(typeof(DomainDbContext))]
    [Migration("20250513073722_Update_idCompany_for_emailconfig")]
    partial class Update_idCompany_for_emailconfig
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                .HasAnnotation("ProductVersion", "3.1.32")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            // Các model khác sẽ được tự động sinh ra khi bạn chạy lệnh tạo migration
            // Đây chỉ là file mẫu
#pragma warning restore 612, 618
        }
    }
}
