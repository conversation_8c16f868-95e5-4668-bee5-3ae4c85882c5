﻿using CMC.TS.COFF.Helper.Enums;
using CMC.TS.COFF.Helper.Model;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Index(nameof(Code))]
    [Index(nameof(NormalizedCode))]
    [Index(nameof(Name))]
    [Index(nameof(NormalizedName))]
    [Index(nameof(Status))]
    [Index(nameof(ScreenId))]
    [Index(nameof(IsDefault))]
    [Table("FormDefinition")]
    public class FormDefinition : TrackingSystem
    {
        /// <summary>
        /// Key table
        /// </summary>
        [Key]
        [MaxLength(450)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// Mã giao diện
        /// </summary>
        private string _Code = string.Empty;
        [MaxLength(255)]
        [Required]
        public string Code
        {
            get
            {
                return _Code;
            }
            set
            {
                _Code = value;
                NormalizedCode = value.ToUpperInvariant();
            }
        }
        [MaxLength(255)]
        [Required]
        public string NormalizedCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên giao diện
        /// </summary>
        private string _Name = string.Empty;
        [Required]
        [MaxLength(500)]
        public string Name
        {
            get
            {
                return _Name;
            }
            set
            {
                _Name = value;
                NormalizedName = value.ToUpperInvariant();
            }
        }
        [Required]
        [MaxLength(500)]
        public string NormalizedName { get; set; } = string.Empty;
        /// <summary>
        /// Mô tả
        /// </summary>
        [MaxLength(2000)]
        public string? Description { get; set; }
        /// <summary>
        /// Số thứ tự
        /// </summary>
        public int? SeqNo { get; set; } = 0;
        /// <summary>
        /// Trạng thái: hoạt động/không hoạt động
        /// </summary>
        public ItemStatus Status { get; set; } = ItemStatus.Active;
        /// <summary>
        /// Giá trị khởi tạo
        /// </summary>
        public string InitValue { get; set; } = "{}";
        /// <summary>
        /// Định nghĩa giao diện
        /// </summary>
        [Required]
        public string Definition { get; set; } = "{}";
        /// <summary>
        /// Màn hình
        /// </summary>
        [Required]
        [MaxLength(450)]
        [ForeignKey("Screen")]
        public string ScreenId { get; set; } = string.Empty;
        /// <summary>
        /// Đơn vị
        /// </summary>
        public string OrgCodes { get; set; } = "{}";
        /// <summary>
        /// Vai trò
        /// </summary>
        public string Roles { get; set; } = "{}";
        /// <summary>
        /// Người dùng
        /// </summary>
        public string Users { get; set; } = "{}";
        /// <summary>
        /// Flag: màn hình mặc định
        /// </summary>
        public bool IsDefault { get; set; } = false;
        /// <summary>
        /// Loại thiết bị
        /// </summary>
        public int? DeviceType { get; set; }
        /// <summary>
        /// Công thức/Điều kiện
        /// </summary>
        [MaxLength(2000)]
        public string? Formular { get; set; }
        public virtual required Screen Screen { get; set; }
    }
}
