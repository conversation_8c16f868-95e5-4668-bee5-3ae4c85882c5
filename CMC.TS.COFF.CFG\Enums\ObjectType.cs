﻿using System.ComponentModel;

namespace CMC.TS.COFF.CFG.Enums
{
    /// <summary>
    /// List Object Type
    /// </summary>
    public enum ObjectType
    {
        DocumentStore = 0, // Kho nội dung
        DocumentType = 1, // Loại Văn bản
        DocumentRegister = 2, // Sổ Văn bản
        Document = 3, // V<PERSON>n bản, Tài liệu
        Category = 4, // Nhóm danh mục
        Catalog = 5, // Danh mục

        Organization = 10, // Đơn vị
        LegalDepartment = 11, // Bộ phận có pháp nhân
        Department = 12, // Bộ phận
        Role = 13, // Vai trò
        Group = 14, // Nhóm người dùng
        User = 15, // Người dùng
        UserInRole = 16, // Vai trò của người dùng
    }

    public enum Status
    {
        Active = 0, // Số tăng
        InActive = 1, // Trùng       
    }

    public enum ConfigType
    {
        Sequence = 0, // Số tăng
        Duplicate = 1, // Trùng       
    }

    public enum SaveMode
    {
        Create,
        Update,
        Delete
    }

    public enum SmsConfigActionType
    {
        [Description("Chuyển tiếp")]
        Forward = 1,
        [Description("Thu hồi")]
        Recall = 2,
        [Description("Phát hành")]
        Publish = 3,
    }

    public enum EmailConfigActionType
    {
        [Description("Chuyển tiếp")]
        Forward = 1,
        [Description("Hoàn thành ký")]
        Recall = 2,
        [Description("Phát hành")]
        Publish = 3,
        //[Description("Bị gửi trả")]
        //Return = 5,
        //[Description("Đang xử lý")]
        //Processing = 7,
        //[Description("Đã xử lý")]
        //Finished = 8,
        //[Description("Đ/y phê duyệt")]
        //Approved = 12,
        //[Description("Y/c hoàn thiện")]
        //Backward = 13,
        ////[Description("Phân công")]
        ////Assignment = 17,
        ////[Description("Hoàn thành")]
        ////Complete = 19,
        //[Description("L/đ có ý kiến xử lý")]
        //LeaderOpinion = 6,
        //[Description("Phê duyệt(Lịch)")] //Áp dụng cho lịch
        //ApproveForCal = 50,
        //[Description("Hủy(Lịch)")] //Áp dụng cho lịch
        //CancelForCal = 51,
        //[Description("Đánh giá(Phiếu giao việc)")] //Áp dụng cho phiếu giao việc.
        //EvaluateForPGV = 52,
        //[Description("Cập nhật tiến độ(Phiếu giao việc)")] //Áp dụng cho phiếu giao việc.
        //UpdateProgressForPGV = 53,
        //[Description("Đánh giá hoàn thành(Phiếu giao việc)")] //Áp dụng cho phiếu giao việc.
        //EvaluateCompletedForPGV = 54,
        //[Description("Đánh giá chưa hoàn thành(Phiếu giao việc)")] //Áp dụng cho phiếu giao việc.
        //EvaluateNotCompletedForPGV = 55,
        //[Description("Chờ cấp số(Tờ trình)")]
        //WaitProgressNumber = 70,
        //[Description("Lãnh đạo phối hợp kết thúc(Tờ trình lãnh đạo)")]
        //SubmissionDocLeaderCooFinish = 71,
        //[Description("Chuyển đơn vị bổ sung(Văn bản đến)")]
        //IncomingDocsAddUnit = 72,
        //[Description("Trả lại(Văn bản đi)")]
        //OutgoingDocReject = 73,
        //[Description("Nhắc việc đến hạn xử lý")]
        //RemindPrevDueDate = 100,
        //[Description("Nhắc việc đến hạn y/c trình lại(Tờ trình)")]
        //RemindPrevDueDateTTr = 101,
    }
    public enum SmsConfigObjectType
    {
        [Description("Người kế trước")]
        Prev = 4,
        [Description("Người soạn thảo")]
        Editor = 5
    }

    public enum EmailConfigObjectType
    {
        //[Description("Lãnh đạo")]
        //LeaderRecipient = 1,
        //[Description("Chủ trì")]
        //PresideRecipients = 2,
        //[Description("Phối hợp")]
        //CoordinatedRecipients = 3,
        //[Description("Thông báo")]
        //NotifierRecipients = 4,
        [Description("Người kế trước")]
        Prev = 4,
        [Description("Người soạn thảo")]
        Editor = 5,
        [Description("Tham gia quy trình")]
        All = 6,
        [Description("Người kế sau")]
        Next = 7,
        [Description("Tham gia quy trình con")]
        SubAll = 8,
        [Description("Người hiện tại")]
        Current = 9
    }

    public enum SmsConfigActionNoti
    {
        [Description("Thư điện tử")]
        Email = 1,
        [Description("Thông báo")]
        Noti = 2,
        [Description("Tất cả")]
        All = 3,
    }

    public enum EmailConfigActionNoti
    {
        [Description("Thư điện tử")]
        Email = 1,
        [Description("Thông báo")]
        Noti = 2,
        [Description("Tất cả")]
        All = 3,
    }
    public enum EmailConfigTargetType
    {
        [Description("Vai trò")]
        Role = 1,
        [Description("Người dùng")]
        User = 2,
    }
    public enum EmailConfigDateBy
    {
        [Description("Không xử lý")]
        NotProcess = 0,
        [Description("Hạn xử lý")]
        DueDate = 2,
        [Description("Ngày văn bản")]
        DocDate = 3,
    }
    public enum EmailConfigDateType
    {
        [Description("Giờ cố định")]
        FixedHour = 1,
        [Description("Giờ giãn cách")]
        SpaceHour = 2,
    }
    public enum EmailConfigDateLoop
    {
        [Description("Ngày")]
        Day = 1,
        [Description("Giờ")]
        Hour = 2,
    }
    public enum EmailConfigDateOperatorCompare
    {
        [Description("Nhỏ hơn")]
        Less = 1,
        [Description("Nhỏ hơn hoặc bằng")]
        LessOrEqual = 2,
        [Description("Bằng")]
        Equal = 3,
        [Description("Lớn hơn")]
        More = 4,
        [Description("Lớn hơn hoặc bằng")]
        MoreOrEqual = 5,
    }
    public enum FormDefinitionType
    {
        [Description("Mặc định")]
        None = 0,
        [Description("Tạo mới")]
        New = 1,
        [Description("Cập nhật")]
        Edit = 2,
        [Description("Chỉ xem")]
        Readonly = 3,
        [Description("Tiếp tục")]
        Continue = 4,
        [Description("Danh sách")]
        List = 5,
    }   
    public enum FormStatusType
    {
        [Description("Đang hoạt động")]
        Active = 0,
        [Description("Không hoạt đông")]
        InActive = 1,
        [Description("Ưu tiên")]
        Priority = 2,
        [Description("Không ưu tiên")]
        NoPriority = 3,
        [Description("Đã xóa")]
        Deleted = 4,
        [Description("Unknown")]
        Unknown = 5,
    }
    public enum ClientType
    {
        [Description("Web")]
        Web = 0,
        [Description("Mobile")]
        Mobile = 1,
        [Description("Other")]
        Other = 2,
    }
    public enum FormLibraryType
    {
        Form = 0,
        FieldChange = 1,
        InitState = 2,
        EventHandler = 3
    }
}
