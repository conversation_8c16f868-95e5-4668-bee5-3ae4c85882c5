﻿using CMC.TS.COFF.CFG.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface IMenuSet
    {
        Task<IActionResult> CreateAsync(HttpContext httpContext, Biz.Model.MenuSet.New model);
        Task<IActionResult> GetAsync(HttpContext httpContext, string id);
        Task<IActionResult> UpdateAsync(HttpContext httpContext, Biz.Model.MenuSet.Edit model);
        Task<IActionResult> DeleteAsync(HttpContext httpContext, string id);
        Task<IActionResult> GetAsync(HttpContext httpContext, Biz.Model.MenuSet.Filter filter);
        Task<IActionResult> MenuSetDefinitionAsync(HttpContext httpContext, string id);
        Task<IActionResult> ItemDefinitionAsync(HttpContext httpContext, string menusetId, string itemId);
        Task<IActionResult> ListDefinitionAsync(HttpContext httpContext, Biz.Model.MenuSet.FilterDefs filter);
        Task<IActionResult> CreateDefinition(HttpContext httpContext, MenuModel model, string menusetId);
        Task<IActionResult> UpdateDefinition(HttpContext httpContext, MenuModel model, string menusetId);
        Task<IActionResult> DeleteDefinition(HttpContext httpContext, Biz.Model.MenuSet.RemoveModel model);
        Task<IActionResult> UserMenu(HttpContext httpContext);
    }
}
