﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface ISmsConfig
    {
        Task<(Model.SmsConfig.View view, string errorMessage)> CreateAsync(Model.SmsConfig.New model);
        Task<(Model.SmsConfig.View view, string errorMessage)> UpdateAsync(Model.SmsConfig.Edit model);
        Task<bool> DeleteAsync(long id);
        Task<Model.SmsConfig.View> GetAsync(long id);
        Task<List<Model.SmsConfig.View>> GetAllAsync();
        Task MemoryStreamToEntities(byte[] content);
        Task<byte[]> EntitiesToMemoryStream();
    }
}
