﻿using Microsoft.AspNetCore.Http;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface ISmsConfig
    {
        Task<(Model.SmsConfig.View view, string errorMessage)> CreateAsync(HttpContext context, Model.SmsConfig.New model);
        Task<(Model.SmsConfig.View view, string errorMessage)> UpdateAsync(HttpContext context, Model.SmsConfig.Edit model);
        Task<bool> DeleteAsync(HttpContext context, long id);
        Task<Model.SmsConfig.View> GetAsync(long id);
        Task<List<Model.SmsConfig.View>> GetAllAsync(HttpContext context);
        Task MemoryStreamToEntities(byte[] content);
        Task<byte[]> EntitiesToMemoryStream();
    }
}
