using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.Helper.Biz;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace CMC.TS.EDOC.CFG.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CommonController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private IConfiguration _configuration;
        private readonly ILogger<CommonController> _logger;
        public CommonController(IRepositoryWrapper repository, ILogger<CommonController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _configuration = configuration;
            _logger = logger;
        }
  
        [HttpGet("GetValuesEnum")]
        public IActionResult GetValuesEnum(string type, string? values)
        {
            try
            {

                    var item = _repository.CommonRepository.GetValuesEnum(type, values);
                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
    }
}
