﻿using CMC.TS.COFF.Helper.Model;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CMC.TS.COFF.CFG.Data.Model
{
    [Table("ConfigParameter")]
    public class ConfigParameter : TrackingSystem
    {
        [Key]
        [MaxLength(450)]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [MaxLength(1)]
        public string RequireDepartmentSelection { get; set; } = "0";

        [MaxLength(1)]
        public string AutoContractNumberType { get; set; } = "0";

        [MaxLength(255)]
        public string ContractNumberSuffix { get; set; } = "0";

        [MaxLength(1)]
        public string LockPartnerSignaturePosition { get; set; } = "0";

        [MaxLength(1)]
        public string UseEkyc { get; set; } = "0";

        [MaxLength(1)]
        public string UseContractForm { get; set; } = "0";

        [MaxLength(1)]
        public string UseContractTemplate { get; set; } = "0";

        [MaxLength(1)]
        public string UseContractType { get; set; } = "0";

        [MaxLength(1)]
        public string RequireRelatedInfoSelection { get; set; } = "0";

        [MaxLength(1)]
        public string UseDepartmentOnContractCreation { get; set; } = "0";

        [MaxLength(1)]
        public string AllowDepartmentSelection { get; set; } = "0";

        [MaxLength(1)]
        public string ShowSignersList { get; set; } = "0";

        [MaxLength(1)]
        public string SelectSignersByCompany { get; set; } = "0";

        [MaxLength(1)]
        public string FilterSignersByDepartment { get; set; } = "0";

        [MaxLength(255)]
        public string EmailNotification { get; set; }

        [MaxLength(500)]
        public string? ApiKey { get; set; }

        [MaxLength(1)]
        public string PreventPartnerDrawSignature { get; set; } = "0";

        [MaxLength(1)]
        public string PreventPartnerUploadSignature { get; set; } = "0";

        [MaxLength(1)]
        public string UseExpiringSignLink { get; set; } = "0";

        public int? LinkExpirationDays { get; set; }

        public double? SignatureWidth { get; set; }

        [MaxLength(1)]
        public string UseContractExpirationDate { get; set; } = "0";


        public int? ContractExpirationDays { get; set; }

        public double? SignatureHeight { get; set; }

        [MaxLength(1)]
        public string UseWorkflowByContractType { get; set; } = "0";

        [MaxLength(1)]
        public string AutoSelectDepartmentByCreator { get; set; } = "0";

        [MaxLength(1)]
        public string AutoSelectCompanyByCreator { get; set; } = "0";

        [MaxLength(1)]
        public string ReportUsersByContractType { get; set; } = "0";

        [MaxLength(1)]
        public int reminderStartDaysBeforeExpiration { get; set; }

        [MaxLength(1)]
        public int reminderFrequency { get; set; }

        [Required]
        [MaxLength(255)]

        public string Domain { get; set; }

        [Required]
        [MaxLength(50)]
        public string Port { get; set; }

        [Required]
        [MaxLength(255)]
        public string Address { get; set; }

        [Required]
        [MaxLength(255)]
        public string? LoginName { get; set; }

        [Required]

        public string? Password { get; set; }

        [Required]
        public int? SSL { get; set; }

        [MaxLength(255)]
        public string Name { get; set; }

        [Required]
        [MaxLength(50)]
        public string ? EmailTest { get; set; }


    }
}