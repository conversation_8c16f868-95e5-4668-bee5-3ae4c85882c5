﻿using CMC.TS.COFF.CFG.Biz;
using CMC.TS.COFF.Helper.Biz;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SmsConfigController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<SmsConfigController> _logger;
        private readonly IConfiguration _configuration;

        public SmsConfigController(IRepositoryWrapper repository, ILogger<SmsConfigController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// L<PERSON>y thông tin SMS Config theo ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(long id)
        {
            _logger.LogInformation($"Start GetById {id}");
            try
            {
                var item = await _repository.SmsConfig.GetAsync(id);
                if (item == null)
                {
                    return ResponseMessageWrapper.BuildNotFound("SMS Config không tồn tại");
                }
                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Get SmsConfig {id} Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Lấy danh sách tất cả SMS Config
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            _logger.LogInformation($"Start GetAll SmsConfig");
            try
            {
                var items = await _repository.SmsConfig.GetAllAsync(HttpContext);
                return ResponseMessageWrapper.BuildSuccess(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get All SmsConfig Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }


        /// <summary>
        /// Tạo mới SMS Config
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] COFF.CFG.Biz.Model.SmsConfig.New model)
        {
            _logger.LogInformation($"Start Create SmsConfig");
            try
            {
                // Validate model
                if (model == null)
                {
                    return ResponseMessageWrapper.BuildBadRequest("Dữ liệu không hợp lệ");
                }



                // Kiểm tra ContentVi
                if (string.IsNullOrEmpty(model.ContentVi))
                {
                    return ResponseMessageWrapper.BuildBadRequest("Nội dung tin nhắn tiếng Việt không được để trống");
                }

                var (item, errorMessage) = await _repository.SmsConfig.CreateAsync(HttpContext, model);
                if (item == null)
                {
                    return ResponseMessageWrapper.BuildBadRequest(errorMessage ?? "Không thể tạo SMS Config");
                }

                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Create SmsConfig Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Cập nhật SMS Config
        /// </summary>
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] COFF.CFG.Biz.Model.SmsConfig.Edit model)
        {

            try
            {
                // Validate model
                if (model == null)
                {
                    return ResponseMessageWrapper.BuildBadRequest("Dữ liệu không hợp lệ");
                }

                _logger.LogInformation($"Đang cập nhật SmsConfig với ID = {model.ID}");




                // Kiểm tra ContentVi
                if (string.IsNullOrEmpty(model.ContentVi))
                {
                    return ResponseMessageWrapper.BuildBadRequest("Nội dung tin nhắn tiếng Việt không được để trống");
                }

                var (item, errorMessage) = await _repository.SmsConfig.UpdateAsync(HttpContext, model);
                if (item == null)
                {
                    return ResponseMessageWrapper.BuildBadRequest(errorMessage ?? "SMS Config không tồn tại");
                }

                return ResponseMessageWrapper.BuildSuccess(item);
            }
            catch (Exception ex)
            {

                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }

        /// <summary>
        /// Xóa SMS Config
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            _logger.LogInformation($"Start Delete SmsConfig {id}");
            try
            {
                var result = await _repository.SmsConfig.DeleteAsync(HttpContext, id);
                if (!result)
                {
                    return ResponseMessageWrapper.BuildNotFound("SMS Config không tồn tại");
                }

                return ResponseMessageWrapper.BuildSuccess(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Delete SmsConfig {id} Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }


        /// <summary>
        /// Import SMS Config từ file
        /// </summary>
        [HttpPost("import")]
        public async Task<IActionResult> Import(IFormFile file, [FromHeader] string apiKey)
        {
            _logger.LogInformation($"Start Import SmsConfig");

            // Kiểm tra apiKey
            if (string.IsNullOrEmpty(apiKey) || !_configuration["ApiKey"].Equals(apiKey))
            {
                return Unauthorized();
            }

            // Kiểm tra file
            if (file == null || file.Length == 0)
            {
                return ResponseMessageWrapper.BuildBadRequest("File không hợp lệ");
            }

            try
            {
                using (var ms = new MemoryStream())
                {
                    await file.CopyToAsync(ms);
                    var fileBytes = ms.ToArray();
                    await _repository.SmsConfig.MemoryStreamToEntities(fileBytes);
                    return ResponseMessageWrapper.BuildSuccess("Import thành công");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Import SmsConfig Error");
                return ResponseMessageWrapper.BuildErrorResponse(ex);
            }
        }
    }
}
