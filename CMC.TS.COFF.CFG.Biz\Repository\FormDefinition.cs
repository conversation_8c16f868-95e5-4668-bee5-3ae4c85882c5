﻿using AutoMapper;
using CMC.TS.COFF.CFG.Biz.IRepository;
using CMC.TS.COFF.CFG.Data;
using CMC.TS.COFF.Helper.Biz.RepositoryBase;
using CMC.TS.COFF.Helper.Cache.Services;
using CMC.TS.COFF.Helper.Model;
using CMC.TS.COFF.Helper.Tenant;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CMC.TS.COFF.CFG.Biz.Repository
{
    public class FormDefinition : RepositoryBase<Data.Model.FormDefinition, string>, IFormDefinition
    {
        private readonly DomainDbContext _context;
        private readonly ILogger<RepositoryWrapper> _logger;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCacheService _cacheService;
        protected readonly ITenantService _tenantService;
        public FormDefinition(DomainDbContext context, ILogger<RepositoryWrapper> logger, IMapper mapper, IConfiguration configuration, IDistributedCacheService cacheService, ITenantService tenantService) : base(context, mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _configuration = configuration;
            _cacheService = cacheService;
            _tenantService = tenantService;
        }

        public override Task<PagedResult<Data.Model.FormDefinition>> GetItemsAsync(PagingRequest model)
        {
            throw new NotImplementedException();
        }

        public override Task<PagedResult<TListModel>> GetItemsAsync<TListModel>(PagingRequest model)
        {
            throw new NotImplementedException();
        }
    }
}
