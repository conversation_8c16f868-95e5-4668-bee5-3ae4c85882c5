﻿using CMC.TS.COFF.Helper.Model;

namespace CMC.TS.COFF.CFG.Biz.Model.ConfigParameter
{
    public class New : TNewModelBase<string>
    {
        public string RequireDepartmentSelection { get; set; } = "0";
        public string AutoContractNumberType { get; set; } = "0";
        public string ContractNumberSuffix { get; set; } = "0";
        public string LockPartnerSignaturePosition { get; set; } = "0";
        public string UseEkyc { get; set; } = "0";
        public string UseContractForm { get; set; } = "0";
        public string UseContractTemplate { get; set; } = "0";
        public string UseContractType { get; set; } = "0";
        public string RequireRelatedInfoSelection { get; set; } = "0";
        public string UseDepartmentOnContractCreation { get; set; } = "0";
        public string AllowDepartmentSelection { get; set; } = "0";
        public string ShowSignersList { get; set; } = "0";
        public string SelectSignersByCompany { get; set; } = "0";
        public string FilterSignersByDepartment { get; set; } = "0";
        public string EmailNotification { get; set; }
        public string? ApiKey { get; set; }
        public string PreventPartnerDrawSignature { get; set; } = "0";
        public string PreventPartnerUploadSignature { get; set; } = "0";
        public string UseExpiringSignLink { get; set; } = "0";
        public int? LinkExpirationDays { get; set; }
        public double? SignatureWidth { get; set; }
        public double? SignatureHeight { get; set; }
        public string UseWorkflowByContractType { get; set; } = "0";
        public string AutoSelectDepartmentByCreator { get; set; } = "0";
        public string AutoSelectCompanyByCreator { get; set; } = "0";
        public string ReportUsersByContractType { get; set; } = "0";
        public int ContractExpirationDays { get; set; }
        public string UseContractExpirationDate { get; set; } = "0";
        public int reminderStartDaysBeforeExpiration { get; set; }
        public int reminderFrequency { get; set; }

        public string Domain { get; set; }
        public string Port { get; set; }
        public string Address { get; set; }
        public string? LoginName { get; set; }
        public string? Password { get; set; }
        public int? SSL { get; set; }
        public string Name { get; set; }
        public string? EmailTest { get; set; }
    }
}