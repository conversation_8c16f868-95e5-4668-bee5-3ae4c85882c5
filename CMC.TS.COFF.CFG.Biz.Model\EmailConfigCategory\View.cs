using CMC.TS.COFF.CFG.Biz.Model.EmailConfig;
using CMC.TS.COFF.Helper.Model;
using ProtoBuf;
using System;
using System.Collections.Generic;

namespace CMC.TS.COFF.CFG.Biz.Model.EmailConfigCategory
{
    [ProtoContract]
    public class View : TViewModelBase<string>
    {
        [ProtoMember(1)]
        public long ID { get; set; }

        [ProtoMember(2)]
        public string Code { get; set; } = string.Empty;

        [ProtoMember(3)]
        public string Name { get; set; } = string.Empty;

        [ProtoMember(4)]
        public bool IsValid { get; set; } = true;

        [ProtoMember(5)]
        public bool IsDefault { get; set; }

        [ProtoMember(6)]
        public string Description { get; set; } = string.Empty;

        [ProtoMember(7)]
        public string UserID { get; set; } = string.Empty;

        [ProtoMember(8)]
        public DateTime CreatedAt { get; set; }

        [ProtoMember(9)]
        public DateTime? UpdatedAt { get; set; }

        [ProtoMember(10)]
        public List<EmailConfig.View> EmailConfigs { get; set; } = new List<EmailConfig.View>();
    }
}
