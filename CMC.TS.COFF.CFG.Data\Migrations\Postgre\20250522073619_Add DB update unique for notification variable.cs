﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBupdateuniquefornotificationvariable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_ID",
                table: "SmsNotificationVariable");

            migrationBuilder.DropIndex(
                name: "IX_NOTIFICATION_VARIABLE_EMAIL_CONFIG_ID",
                table: "NotificationVariable");

            migrationBuilder.CreateIndex(
                name: "UK_SmsConfigID_VariableKey",
                table: "SmsNotificationVariable",
                columns: new[] { "SMS_CONFIG_ID", "VARIABLE_KEY" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "UK_EmailConfigID_VariableKey",
                table: "NotificationVariable",
                columns: new[] { "EMAIL_CONFIG_ID", "VARIABLE_KEY" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "UK_SmsConfigID_VariableKey",
                table: "SmsNotificationVariable");

            migrationBuilder.DropIndex(
                name: "UK_EmailConfigID_VariableKey",
                table: "NotificationVariable");

            migrationBuilder.CreateIndex(
                name: "IX_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_ID",
                table: "SmsNotificationVariable",
                column: "SMS_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_NOTIFICATION_VARIABLE_EMAIL_CONFIG_ID",
                table: "NotificationVariable",
                column: "EMAIL_CONFIG_ID");
        }
    }
}
