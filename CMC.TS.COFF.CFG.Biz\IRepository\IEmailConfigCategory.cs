using CMC.TS.COFF.Helper.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface IEmailConfigCategory
    {
        Task<Biz.Model.EmailConfigCategory.View> CreateAsync(Biz.Model.EmailConfigCategory.New model);
        Task<Biz.Model.EmailConfigCategory.View> UpdateAsync(Biz.Model.EmailConfigCategory.Edit model);
        Task<Biz.Model.EmailConfigCategory.View> GetAsync(long id);
        Task<PagedResult<Biz.Model.EmailConfigCategory.View>> GetAsync(int page, int pageSize);
        Task<List<Biz.Model.EmailConfigCategory.ListItem>> GetAllCategoriesOnlyAsync();
        Task<bool> DeleteAsync(long id);
        Task<IActionResult> ShortInfoAsync();
    }
}
