﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Biz.IRepository
{
    public interface IEmailConfig
    {
        Task<Model.EmailConfig.View> CreateAsync(Model.EmailConfig.New model);
        Task<Model.EmailConfig.View> UpdateAsync(Model.EmailConfig.Edit model);
        Task<Model.EmailConfig.View> GetAsync(HttpContext httpContext, long id);
        Task<Model.EmailConfig.View> GetByCodeAsync(string code);
        Task<List<Model.EmailConfig.View>> GetByEmailConfigCategoryIdAsync(HttpContext httpContext, long emailConfigCategoryId);
        Task MemoryStreamToEntities(byte[] content);
        Task<Biz.Model.EmailConfig.ShortInfo> GetByEmailCategoryAndOrgCodeAsync(HttpContext httpContext, string? orgCode, string code);
        Task<Dictionary<string, string>> GetNotificationVariablesAsync(long emailConfigId);

        // Delete EmailConfig and all related data
        Task<bool> DeleteAsync(long id);
    }
}
