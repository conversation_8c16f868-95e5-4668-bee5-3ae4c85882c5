﻿using System;
using CMC.TS.COFF.Helper.Models;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class Addpropetiesforsmsconfiganddeletenotification : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NotificationVariable");

            migrationBuilder.DropTable(
                name: "SmsNotificationVariable");

            migrationBuilder.DropColumn(
                name: "UPDATED_AT",
                table: "SmsConfig");

            migrationBuilder.RenameColumn(
                name: "CREATED_AT",
                table: "SmsConfig",
                newName: "MODIFIED");

            migrationBuilder.AddColumn<DateTime>(
                name: "CREATED",
                table: "SmsConfig",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Person>(
                name: "CREATED_BY",
                table: "SmsConfig",
                type: "jsonb",
                nullable: false);

            migrationBuilder.AddColumn<string>(
                name: "CREATED_BY_ID",
                table: "SmsConfig",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Person>(
                name: "MODIFIED_BY",
                table: "SmsConfig",
                type: "jsonb",
                nullable: false);

            migrationBuilder.AddColumn<string>(
                name: "MODIFIED_BY_ID",
                table: "SmsConfig",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "NAME",
                table: "SmsConfig",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MODIFIED_BY",
                table: "EmailConfigCategory",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "CREATED_BY",
                table: "EmailConfigCategory",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "MODIFIED_BY",
                table: "EmailConfig",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "CREATED_BY",
                table: "EmailConfig",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "MODIFIED_BY",
                table: "ConfigParameter",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "CREATED_BY",
                table: "ConfigParameter",
                type: "jsonb",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CREATED",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "CREATED_BY",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "CREATED_BY_ID",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "MODIFIED_BY_ID",
                table: "SmsConfig");

            migrationBuilder.DropColumn(
                name: "NAME",
                table: "SmsConfig");

            migrationBuilder.RenameColumn(
                name: "MODIFIED",
                table: "SmsConfig",
                newName: "CREATED_AT");

            migrationBuilder.AddColumn<DateTime>(
                name: "UPDATED_AT",
                table: "SmsConfig",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MODIFIED_BY",
                table: "EmailConfigCategory",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<string>(
                name: "CREATED_BY",
                table: "EmailConfigCategory",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<string>(
                name: "MODIFIED_BY",
                table: "EmailConfig",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<string>(
                name: "CREATED_BY",
                table: "EmailConfig",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<string>(
                name: "MODIFIED_BY",
                table: "ConfigParameter",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<string>(
                name: "CREATED_BY",
                table: "ConfigParameter",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.CreateTable(
                name: "NotificationVariable",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EMAIL_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    VARIABLE_KEY = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    VARIABLE_VALUE = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NOTIFICATION_VARIABLE", x => x.ID);
                    table.ForeignKey(
                        name: "FK_NOTIFICATION_VARIABLE_EMAIL_CONFIG_EMAIL_CONFIG_ID",
                        column: x => x.EMAIL_CONFIG_ID,
                        principalTable: "EmailConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SmsNotificationVariable",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    SMS_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    VARIABLE_KEY = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    VARIABLE_VALUE = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SMS_NOTIFICATION_VARIABLE", x => x.ID);
                    table.ForeignKey(
                        name: "FK_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_SMS_CONFIG_ID",
                        column: x => x.SMS_CONFIG_ID,
                        principalTable: "SmsConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NOTIFICATION_VARIABLE_EMAIL_CONFIG_ID",
                table: "NotificationVariable",
                column: "EMAIL_CONFIG_ID");

            migrationBuilder.CreateIndex(
                name: "IX_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_ID",
                table: "SmsNotificationVariable",
                column: "SMS_CONFIG_ID");
        }
    }
}
