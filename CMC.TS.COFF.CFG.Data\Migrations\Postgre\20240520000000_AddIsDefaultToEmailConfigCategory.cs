using Microsoft.EntityFrameworkCore.Migrations;

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddIsDefaultToEmailConfigCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Thêm cột IS_DEFAULT vào bảng EmailConfigCategory
            migrationBuilder.AddColumn<bool>(
                name: "IS_DEFAULT",
                table: "EmailConfigCategory",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Xóa cột IS_DEFAULT khỏi bảng EmailConfigCategory
            migrationBuilder.DropColumn(
                name: "IS_DEFAULT",
                table: "EmailConfigCategory");
        }
    }
}
