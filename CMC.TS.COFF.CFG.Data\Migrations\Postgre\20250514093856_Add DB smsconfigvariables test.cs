﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    /// <inheritdoc />
    public partial class AddDBsmsconfigvariablestest : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SmsNotificationVariable",
                columns: table => new
                {
                    ID = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    SMS_CONFIG_ID = table.Column<long>(type: "bigint", nullable: false),
                    VARIABLE_KEY = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    VARIABLE_VALUE = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DESCRIPTION = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CREATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UPDATED_AT = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SMS_NOTIFICATION_VARIABLE", x => x.ID);
                    table.ForeignKey(
                        name: "FK_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_SMS_CONFIG_ID",
                        column: x => x.SMS_CONFIG_ID,
                        principalTable: "SmsConfig",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SMS_NOTIFICATION_VARIABLE_SMS_CONFIG_ID",
                table: "SmsNotificationVariable",
                column: "SMS_CONFIG_ID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SmsNotificationVariable");
        }
    }
}
