using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using System;

namespace CMC.TS.COFF.CFG.Data.Migrations.Postgre
{
    public partial class UpdateSmsConfig : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Xóa bảng SmsConfig cũ
            migrationBuilder.DropTable(
                name: "SmsConfig");

            // Tạo bảng SmsConfig mới
            migrationBuilder.CreateTable(
                name: "SmsConfig",
                columns: table => new
                {
                    ID = table.Column<long>(nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MESSAGE_TYPE = table.Column<int>(nullable: false),
                    COMPANIES = table.Column<string>(nullable: false),
                    CONTENT_VI = table.Column<string>(nullable: false),
                    CONTENT_EN = table.Column<string>(nullable: true),
                    CREATED_AT = table.Column<DateTime>(nullable: false),
                    UPDATED_AT = table.Column<DateTime>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SmsConfig", x => x.ID);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Xóa bảng SmsConfig mới
            migrationBuilder.DropTable(
                name: "SmsConfig");

            // Tạo lại bảng SmsConfig cũ
            migrationBuilder.CreateTable(
                name: "SmsConfig",
                columns: table => new
                {
                    ID = table.Column<long>(nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WORKFLOW_DEF_ID = table.Column<string>(maxLength: 255, nullable: false),
                    WORKFLOW_DEF_NAME = table.Column<string>(maxLength: 255, nullable: false),
                    ACTION_TYPE = table.Column<int>(nullable: false),
                    OBJECT_TYPE = table.Column<int>(nullable: false),
                    IS_VALID = table.Column<bool>(nullable: false),
                    SUBJECT = table.Column<string>(maxLength: 500, nullable: false),
                    CONTENT = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SmsConfig", x => x.ID);
                });
        }
    }
}
