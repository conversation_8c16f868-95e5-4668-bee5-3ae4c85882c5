﻿using CMC.TS.COFF.CFG.Biz;
using Microsoft.AspNetCore.Mvc;

namespace CMC.TS.COFF.CFG.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ScreenController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;
        private readonly ILogger<ScreenController> _logger;
        private readonly IConfiguration _configuration;
        public ScreenController(IRepositoryWrapper repository, ILogger<ScreenController> logger, IConfiguration configuration)
        {
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }
    }
}
